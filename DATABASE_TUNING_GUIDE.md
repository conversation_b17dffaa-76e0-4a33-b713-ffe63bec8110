# Database Server Tuning Guide for Concurrency Issues

## Overview

This guide addresses database server tuning to reduce lock contention, concurrent operation errors, and improve overall performance for high-concurrency workloads.

## Current Issues Observed

1. **Lock Timeout Errors**: `LockNotAvailableError: canceling statement due to lock timeout`
2. **Concurrent Operation Errors**: `InterfaceError: cannot perform operation: another operation is in progress`
3. **Failed Transaction Errors**: `InFailedSQLTransactionError: current transaction is aborted`

## PostgreSQL Server Configuration

### 1. Lock and Timeout Settings

```sql
-- Check current settings
SHOW lock_timeout;
SHOW deadlock_timeout;
SHOW statement_timeout;

-- Recommended settings
ALTER SYSTEM SET lock_timeout = '30s';          -- Wait max 30s for locks
ALTER SYSTEM SET deadlock_timeout = '5s';       -- Detect deadlocks after 5s
ALTER SYSTEM SET statement_timeout = '300s';    -- Kill statements after 5 minutes
ALTER SYSTEM SET idle_in_transaction_session_timeout = '60s'; -- Kill idle transactions

-- Apply changes
SELECT pg_reload_conf();
```

### 2. Connection and Memory Settings

```sql
-- Connection settings
ALTER SYSTEM SET max_connections = 200;         -- Increase if needed
ALTER SYSTEM SET shared_buffers = '512MB';      -- 25% of RAM (adjust based on your system)
ALTER SYSTEM SET effective_cache_size = '2GB';  -- 75% of RAM (adjust based on your system)
ALTER SYSTEM SET work_mem = '16MB';              -- Per-operation memory
ALTER SYSTEM SET maintenance_work_mem = '256MB'; -- For maintenance operations

-- Apply changes
SELECT pg_reload_conf();
```

### 3. WAL and Checkpoint Settings

```sql
-- Write-Ahead Logging settings for better concurrency
ALTER SYSTEM SET max_wal_size = '2GB';
ALTER SYSTEM SET min_wal_size = '512MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';

-- Apply changes
SELECT pg_reload_conf();
```

### 4. Concurrency and Locking Settings

```sql
-- Improve concurrent access
ALTER SYSTEM SET random_page_cost = 1.1;        -- For SSD storage
ALTER SYSTEM SET seq_page_cost = 1.0;           -- For SSD storage
ALTER SYSTEM SET cpu_tuple_cost = 0.01;         -- Adjust for modern CPUs
ALTER SYSTEM SET cpu_index_tuple_cost = 0.005;  -- Adjust for modern CPUs

-- Apply changes
SELECT pg_reload_conf();
```

## Application-Level Optimizations

### 1. Batch Size Optimization

**Current Issue**: Large batches can hold locks longer
**Solution**: Reduce batch sizes

```python
# In your application configuration
RECOMMENDED_BATCH_SIZES = {
    'issue': 1000,           # Main table - smaller batches
    'issue_links': 2000,     # Child table - moderate batches
    'changelog': 3000,       # Log table - larger batches ok
    'comments': 2000,        # Child table - moderate batches
    'worklog': 2000,         # Child table - moderate batches
}
```

### 2. Transaction Isolation Level

```python
# Consider using READ COMMITTED for better concurrency
# In your SQLAlchemy session configuration:
session = sessionmaker(
    bind=engine,
    isolation_level="READ_COMMITTED"  # Instead of default REPEATABLE_READ
)
```

### 3. Connection Pool Tuning

```python
# SQLAlchemy connection pool settings
engine = create_async_engine(
    database_url,
    pool_size=20,           # Base number of connections
    max_overflow=30,        # Additional connections when needed
    pool_timeout=30,        # Wait time for connection
    pool_recycle=3600,      # Recycle connections every hour
    pool_pre_ping=True      # Validate connections before use
)
```

## Monitoring and Diagnostics

### 1. Lock Monitoring Queries

```sql
-- Check current locks
SELECT 
    pg_stat_activity.pid,
    pg_stat_activity.usename,
    pg_stat_activity.query,
    pg_locks.mode,
    pg_locks.locktype,
    pg_locks.granted
FROM pg_stat_activity 
JOIN pg_locks ON pg_stat_activity.pid = pg_locks.pid
WHERE NOT pg_locks.granted
ORDER BY pg_stat_activity.query_start;

-- Check lock waits
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS blocking_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### 2. Performance Monitoring

```sql
-- Check database statistics
SELECT * FROM pg_stat_database WHERE datname = 'your_database_name';

-- Check table statistics
SELECT * FROM pg_stat_user_tables WHERE schemaname = 'plat';

-- Check index usage
SELECT * FROM pg_stat_user_indexes WHERE schemaname = 'plat';
```

## Index Optimization

### 1. Check for Missing Indexes

```sql
-- Find tables with high sequential scans
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    seq_tup_read / seq_scan as avg_seq_read
FROM pg_stat_user_tables 
WHERE schemaname = 'plat'
    AND seq_scan > 0
ORDER BY seq_tup_read DESC;
```

### 2. Recommended Indexes for Your Tables

```sql
-- Issue table indexes (if not already present)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_parent_id ON plat.issue(parent_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_parent_key ON plat.issue(parent_key);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_updated ON plat.issue(updated);

-- Issue links indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_links_issue_id ON plat.issue_links(issue_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_links_inward_issue_id ON plat.issue_links("inwardIssue_id");
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_issue_links_outward_issue_id ON plat.issue_links("outwardIssue_id");
```

## Emergency Procedures

### 1. Kill Long-Running Queries

```sql
-- Find long-running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
    AND state = 'active';

-- Kill specific query (replace PID)
SELECT pg_terminate_backend(PID);
```

### 2. Clear Locks in Emergency

```sql
-- Find and kill blocking queries
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' 
    AND (now() - query_start) > interval '10 minutes';
```

## Implementation Steps

1. **Apply PostgreSQL configuration changes** (requires restart)
2. **Update application batch sizes** in configuration
3. **Monitor lock waits** using provided queries
4. **Add missing indexes** using CONCURRENTLY option
5. **Test with reduced concurrency** initially
6. **Gradually increase concurrency** while monitoring

## Expected Improvements

- Reduced lock timeout errors
- Faster transaction processing
- Better concurrent operation handling
- Improved overall throughput
- More graceful error recovery

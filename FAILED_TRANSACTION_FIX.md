# Failed Transaction Error Fix

## Problem Description

The application was encountering `InFailedSQLTransactionError: current transaction is aborted, commands ignored until end of transaction block` errors. This occurs when:

1. A database operation fails within a PostgreSQL transaction
2. The transaction enters an "aborted" state
3. All subsequent operations on that transaction fail until it's rolled back
4. The session health check fails because it tries to execute `SELECT 1` on an aborted transaction

## Root Cause

When an error occurred in one of the consumer tasks within the TaskGroup, the database session would enter a failed transaction state. However, the existing error handling didn't immediately detect and handle this by rolling back the transaction, causing subsequent operations to fail with `InFailedSQLTransactionError`.

## Solution Implemented

### 1. Enhanced Session Health Check (`check_session_health`)

**Before:**
- Simple `SELECT 1` test
- Failed immediately on aborted transactions

**After:**
- Detects failed transaction states specifically
- Automatically attempts rollback when `current transaction is aborted` is detected
- Retests session health after rollback
- Provides recovery capability

### 2. Enhanced Upsert Health Check (`upsert_async_with_health_check`)

**Before:**
- Single health check before upsert
- No transaction recovery

**After:**
- Multiple health check retries with transaction recovery
- Specific handling for `InFailedSQLTransactionError`
- Automatic rollback and retry on failed transactions
- Exponential backoff between retries

### 3. Enhanced Upsert Async Error Handling (`upsert_async`)

**Before:**
- Handled deadlocks and connection errors
- No specific failed transaction handling

**After:**
- Added `is_failed_transaction_error` detection
- Automatic rollback on failed transaction errors
- Retry logic with exponential backoff
- Proper error classification for failed transactions

### 4. Enhanced SessionManager Rollback (`rollback_all_sessions`)

**Before:**
- Simple rollback attempt
- Basic error logging

**After:**
- Handles cases where session is already in failed/inactive state
- Graceful handling of "no transaction is active" errors
- Force close sessions if rollback fails
- Better error classification and logging

### 5. Enhanced TaskGroup Error Handling (`consume_issues`)

**Before:**
- Generic exception handling
- Basic critical error detection

**After:**
- Specific detection of failed transaction errors in TaskGroup exceptions
- Automatic rollback of all sessions when failed transactions detected
- Enhanced commit error handling with transaction state detection
- Proper recovery attempts before requesting graceful shutdown

## Key Improvements

### Error Detection
- Added specific detection for `InFailedSQLTransactionError`
- Pattern matching for "current transaction is aborted" messages
- Case-insensitive error message checking

### Recovery Mechanisms
- Automatic transaction rollback on failed states
- Session health restoration after rollback
- Multiple retry attempts with exponential backoff
- Graceful degradation when recovery fails

### Logging and Monitoring
- Detailed logging of transaction state changes
- Clear distinction between recoverable and non-recoverable errors
- Progress tracking through recovery attempts

## Error Flow

### Before Fix:
1. Database error occurs → Transaction enters failed state
2. Session health check fails → `SELECT 1` fails on aborted transaction
3. Subsequent operations fail → `InFailedSQLTransactionError`
4. TaskGroup fails → Process terminates

### After Fix:
1. Database error occurs → Transaction enters failed state
2. Session health check detects failed state → Automatic rollback attempted
3. Session health restored → Operations can continue
4. If recovery fails → Graceful shutdown with proper cleanup

## Testing Recommendations

1. **Unit Tests**: Test each enhanced function with simulated failed transaction states
2. **Integration Tests**: Test full consumer workflow with injected database errors
3. **Load Tests**: Verify behavior under high concurrency with occasional failures
4. **Recovery Tests**: Test automatic recovery from various failure scenarios

## Monitoring

Watch for these log messages to verify the fix is working:

- "Session in failed transaction state, attempting rollback"
- "Successfully rolled back failed transaction"
- "Session health restored after rollback"
- "Failed transaction detected during upsert, attempting recovery"
- "Failed transaction detected in consumer X, rolling back sessions"

## Additional Error Types Handled

### 3. Lock Timeout Errors (`LockNotAvailableError`)

**Problem:**
- `canceling statement due to lock timeout`
- Occurs when operations wait too long for database locks
- Even with separate sessions, lock contention can occur on the same data

**Solution:**
- Added `is_lock_error` detection
- Longer exponential backoff with jitter (3^attempt + random 1-5 seconds)
- Allows more time for locks to be released

### 4. Concurrent Operation Errors (`InterfaceError`)

**Problem:**
- `cannot perform operation: another operation is in progress`
- Multiple operations trying to use the same connection simultaneously
- Session/connection management conflicts

**Solution:**
- Added `is_concurrent_operation_error` detection
- Moderate backoff with jitter (2^attempt + random 0.5-2 seconds)
- Enhanced SessionManager with `_commit_session_with_retry`
- Retry logic in commit operations with brief pauses

## Database Server Tuning Recommendations

### PostgreSQL Configuration Changes

1. **Lock Timeout Settings:**
```sql
-- Increase lock timeout (default is usually 0 = wait forever)
ALTER SYSTEM SET lock_timeout = '30s';

-- Increase deadlock timeout
ALTER SYSTEM SET deadlock_timeout = '5s';

-- Reload configuration
SELECT pg_reload_conf();
```

2. **Connection and Session Settings:**
```sql
-- Increase max connections if needed
ALTER SYSTEM SET max_connections = 200;

-- Increase shared buffers for better caching
ALTER SYSTEM SET shared_buffers = '256MB';

-- Increase work memory for sorting/hashing
ALTER SYSTEM SET work_mem = '16MB';
```

3. **Concurrency Settings:**
```sql
-- Increase checkpoint segments for better write performance
ALTER SYSTEM SET max_wal_size = '2GB';
ALTER SYSTEM SET min_wal_size = '512MB';

-- Reduce checkpoint frequency
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
```

### Application-Level Optimizations

1. **Batch Size Tuning:**
   - Consider reducing batch sizes to minimize lock duration
   - Current max: 65535 parameters / columns
   - Recommended: Start with smaller batches (1000-5000 rows)

2. **Transaction Isolation:**
   - Consider using READ COMMITTED isolation level
   - Avoid long-running transactions

3. **Connection Pooling:**
   - Implement connection pooling if not already done
   - Use pgbouncer or similar for connection management

## Configuration

### Application Settings
- Lock error retry: 3^attempt + random(1-5) seconds backoff
- Concurrent operation retry: 2^attempt + random(0.5-2) seconds backoff
- Session commit retry: 3 attempts with exponential backoff
- Health check retry: 2 attempts with transaction recovery

### Database Monitoring
Monitor these metrics to tune further:
- Lock wait time
- Deadlock frequency
- Connection usage
- Transaction duration
- Buffer hit ratio

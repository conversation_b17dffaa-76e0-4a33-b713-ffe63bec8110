#! /usr/bin/env python3
# coding=utf-8
import asyncio
import atexit
import csv
import functools
import hashlib
import math
import queue
import random
import shutil
import string
import traceback
from asyncio import create_task, Queue, CancelledError
from contextlib import asynccontextmanager
from contextvars import ContextVar
from dataclasses import dataclass

from itertools import islice
from logging import Logger

from threading import Thread
from typing import Any, Dict, Union, Type, Optional, Protocol, List, Callable, Coroutine

import markdownify
import yaml
from asyncpg.exceptions import DeadlockDetectedError
from psycopg2.errors import DeadlockDetected
from sqlalchemy.exc import InterfaceError, DisconnectionError
from sqlalchemy import text
from bs4 import BeautifulSoup

import aiohttp
import os
import sys
from zoneinfo import ZoneInfo
import re
from aiohttp import ClientSession, ClientResponseError
from dateutil import parser
from dependency_injector.containers import DynamicContainer
from dependency_injector.wiring import inject, Provide
from num2words import num2words
import pandas as pd

import time
from datetime import datetime, timezone, timedelta
import logging
from enum import Enum

from pykeepass import PyKeePass
from rich.align import Align
from rich.layout import Layout

from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, BarColumn, TextColumn, SpinnerColumn, TimeElapsedColumn, MofNCompleteColumn, TaskID
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich import print as rprint
from rich.console import Console

from sqlalchemy import (
    event, inspect, case, cast, literal, text,
    null, exc, DDL, true, orm, delete, and_, DateTime, or_, column
)

from sqlalchemy.dialects.postgresql import insert, dialect, TEXT

from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy.orm import aliased, Session

from sqlalchemy.sql.ddl import CreateSchema
from sqlalchemy.sql.elements import BinaryExpression, BooleanClauseList

from sqlalchemy.sql.functions import func

from sqlalchemy.util.queue import Empty

from sqlalchemy_utils import LtreeType, database_exists, create_database

from sqlalchemy.sql import select, update

from sqlalchemy.sql.schema import UniqueConstraint
import sqlalchemy.exc

from passlib.context import CryptContext

# from sqlalchemy.exc import StatementError, OperationalError
# from psycopg2 import OperationalError, ProgrammingError, errorcodes, errors

import numpy as np

from dags.data_pipeline.custom_logger import debug_monitor

try:
    from dbmodels.base import Base
    from dbmodels.user import User
    from dbmodels.allboards import AllBoards
    from dbmodels.versions import Versions
    from dbmodels.worklog import WorkLog, DeletedWorklog
    from dbmodels.issue import (Issue, IssueLinks, IssueFields, IssueComments)
    from dbmodels.issueclassification import IssueClassification
    from dbmodels.changelog import ChangelogJSON
    from dbmodels.initiativeattribute import InitiativeAttribute
    from dbmodels.sprint import Sprint
    from dbmodels.rundetails import RunDetailsJira
    from dbmodels.mixins import HasPrivate
except ModuleNotFoundError:
    from .dbmodels.base import Base
    from .dbmodels.user import User
    from .dbmodels.allboards import AllBoards
    from .dbmodels.versions import Versions
    from .dbmodels.worklog import WorkLog
    from .dbmodels.issue import (Issue, IssueLinks, IssueFields, IssueComments)
    from .dbmodels.issueclassification import IssueClassification
    from .dbmodels.changelog import ChangelogJSON
    from .dbmodels.initiativeattribute import InitiativeAttribute
    from .dbmodels.sprint import Sprint
    from .dbmodels.rundetails import RunDetailsJira
    from .dbmodels.mixins import HasPrivate
try:
    from containers import (
        EntryDetails, KeePassContainer, LoggerContainer,
        QueueContainer, DatabaseSessionManagerContainer, JiraEntryDetailsContainer, ApplicationContainer,
        IssueFieldsContainer, FieldNameExtractor
    )
except ModuleNotFoundError:
    from .containers import (
        EntryDetails, KeePassContainer, LoggerContainer,
        QueueContainer, DatabaseSessionManagerContainer, JiraEntryDetailsContainer, ApplicationContainer,
        IssueFieldsContainer, FieldNameExtractor
    )

# Import debugging utilities
try:
    from debug_utils import (
        debug_async_function, debug_sync_function, debug_http_request,
        debug_db_operation, rate_limit_tracker, log_debug_summary,
        debug_timeout_detection, periodic_stuck_check, debug_queue_operation
)
except ModuleNotFoundError:
    from .debug_utils import (
        debug_async_function, debug_sync_function, debug_http_request,
        debug_db_operation, rate_limit_tracker, log_debug_summary,
        debug_timeout_detection, periodic_stuck_check
    )


# try:
#     from dbmodels.base import Base
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.base import Base
#
# try:
#     from dbmodels.allboards import AllBoards
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.allboards import AllBoards
#
# try:
#     from dbmodels.worklog import WorkLog
#     from dbmodels.gs_jira_issues import GSJiraIssues
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.worklog import WorkLog, GSJiraIssues
#
# try:
#     from dbmodels.sprint import Sprint
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.sprint import Sprint
# try:
#     from dbmodels.user import User
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.user import User
# try:
#     from dbmodels.versions import Versions
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.versions import Versions
# try:
#     from dbmodels.issue import Issue
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.issue import Issue
#
# try:
#     from dbmodels.teams import Teams, NLPTrainingData, RequestTracker
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.teams import Teams, NLPTrainingData, RequestTracker
#
# try:
#     from dbmodels.changelog import ChangeLog
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.changelog import ChangeLog
#
# try:
#     from dbmodels.initiativeattribute import InitiativeAttribute
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.initiativeattribute import InitiativeAttribute
#
# try:
#     from dbmodels.issueclassification import IssueClassification
# except ModuleNotFoundError:
#     from data_pipeline.dbmodels.issueclassification import IssueClassification


# CREDITS: https://stackoverflow.com/questions/32107558/how-do-i-convert-numpy-nan-objects-to-sql-nulls
# This converts np.NAN to nulls.

# Example of an event listener for connection checkout
# Listener functions
# DO NOT DELETE
# @inject
# def before_checkout(
#         dbapi_connection, connection_record, pool,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.debug(f"Connection {dbapi_connection} will be checked out from pool.")
#
# @inject
# def after_checkout(
#         dbapi_connection, connection_record,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.debug(f"Connection {dbapi_connection} has been checked out from pool.")
#
#
# @inject
# def on_connection_invalidated(
#         dbapi_connection, connection_record, pool,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.debug(f"Connection {dbapi_connection} has been invalidated.")
# DO NOT DELETE

@event.listens_for(Session, "do_orm_execute")
def _add_filtering_criteria(execute_state):
    """Intercept all ORM queries.   Add a with_loader_criteria option to all
    of them.

    This option applies to SELECT queries and adds a global WHERE criteria
    (or as appropriate ON CLAUSE criteria for join targets)
    to all objects of a certain class or superclass.

    """

    # the with_loader_criteria automatically applies itself to
    # relationship loads as well including lazy loads.   So if this is
    # a relationship load, assume the option was set up from the top level
    # query.

    if (
            not execute_state.is_column_load
            and not execute_state.is_relationship_load
            and not execute_state.execution_options.get("include_private", False)
    ):
        execute_state.statement = execute_state.statement.options(
            orm.with_loader_criteria(
                HasPrivate,
                lambda cls: cls.public == true(),
                include_aliases=True,
            )
        )


class SessionManager:
    """Manages database sessions for coordinated commits"""
    @inject
    def __init__(self, app_container, project_key: str, logger: Logger = Provide[LoggerContainer.logger]):
        self.logger = logger
        self.app_container = app_container
        self.project_key = project_key
        self.sessions = {}  # consumer_name -> session
        self.session_contexts = {}  # consumer_name -> session context manager
        self.lock = asyncio.Lock()

    async def create_session_for_consumer(self, consumer_name: str):
        """Create and register a session for a consumer"""
        async with self.lock:
            if consumer_name in self.sessions:
                self.logger.debug(f"Session already exists for {consumer_name}")
                return self.sessions[consumer_name]

            # Create session context manager but don't enter it yet
            session_cm = self.app_container.database_rw().update_schema(self.project_key).async_session()
            # Enter the context manager to get the actual session
            session = await session_cm.__aenter__()

            self.sessions[consumer_name] = session
            self.session_contexts[consumer_name] = session_cm
            self.logger.debug(f"Created and registered session for {consumer_name}")
            return session

    async def commit_all_sessions(self):
        """Commit all sessions in the correct order for referential integrity"""
        self.logger.info("Starting coordinated commit of all sessions...")
        for key, value in self.sessions.items():
            self.logger.debug(f"Session for {key} has {len(value.dirty)} dirty objects")

        # Step 1: Commit all Issue table sessions first (handles self-referential constraints)
        issue_sessions = [name for name in self.sessions.keys() if "_issue" in name and "_issue_links" not in name]
        for session_name in issue_sessions:
            await self._commit_session_with_retry(session_name, "Issue")

        # Step 2: Commit child table sessions (order doesn't matter as much since Issue is committed)
        child_session_patterns = ["_changelog", "_worklog", "_comment", "_issue_links"]

        for pattern in child_session_patterns:
            matching_sessions = [name for name in self.sessions.keys() if pattern in name]
            for session_name in matching_sessions:
                await self._commit_session_with_retry(session_name, "Child")

        self.logger.info("All sessions committed successfully")

    async def _commit_session_with_retry(self, session_name: str, session_type: str, max_retries: int = 3):
        """Commit a session with retry logic for concurrent operation errors"""
        for attempt in range(max_retries):
            try:
                await self.sessions[session_name].commit()
                self.logger.info(f"{session_type} session {session_name} committed successfully")
                return
            except Exception as e:
                error_msg = str(e).lower()
                attempt_num = attempt + 1

                # Handle concurrent operation errors
                if ('another operation is in progress' in error_msg or
                    'interfaceerror' in error_msg) and attempt < max_retries - 1:
                    wait_time = 0.5 * (2 ** attempt) + random.uniform(0.1, 0.5)
                    self.logger.warning(
                        f"Concurrent operation detected for {session_name}. "
                        f"Retrying commit in {wait_time:.2f} seconds... (attempt {attempt_num}/{max_retries})"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(f"Failed to commit {session_type} session {session_name}: {e}")
                    await self.rollback_all_sessions()
                    raise

    async def rollback_all_sessions(self):
        """Rollback all sessions in case of error"""
        self.logger.warning("Rolling back all sessions due to error")

        for consumer_name, session in self.sessions.items():
            try:
                await session.rollback()
                self.logger.debug(f"Rolled back session for {consumer_name}")
            except Exception as e:
                error_msg = str(e).lower()

                # Handle cases where session is already in a failed state
                if 'current transaction is aborted' in error_msg or 'no transaction is active' in error_msg:
                    self.logger.debug(f"Session {consumer_name} already in failed/inactive state, rollback not needed: {e}")
                else:
                    self.logger.error(f"Error rolling back {consumer_name}: {e}")

                    # Try to force close the session if rollback fails
                    try:
                        await session.close()
                        self.logger.debug(f"Force closed session for {consumer_name} after rollback failure")
                    except Exception as close_error:
                        self.logger.error(f"Failed to force close session {consumer_name}: {close_error}")

    async def close_all_sessions(self):
        """Close all sessions and their context managers"""
        for consumer_name in list(self.sessions.keys()):
            try:
                session = self.sessions[consumer_name]
                session_cm = self.session_contexts[consumer_name]

                # Exit the context manager properly
                await session_cm.__aexit__(None, None, None)
                self.logger.debug(f"Closed session for {consumer_name}")

                # Remove from tracking
                del self.sessions[consumer_name]
                del self.session_contexts[consumer_name]

            except Exception as e:
                self.logger.error(f"Error closing session for {consumer_name}: {e}")

        self.sessions.clear()
        self.session_contexts.clear()

class DataframeDebugger:
    """
    A class to debug and save DataFrame objects to CSV or Excel files.

    Attributes:
    path : str
        The directory path where files will be saved.
    queue : Queue
        A queue to handle file saving tasks.
    worker_thread : Thread
        A thread to process the queue.
    """

    def __init__(self, path=None, max_retries=5, retry_delay=2):
        """
        Initializes the DataFrameDebugger with a specified path.

        Parameters:
        path (str): The directory path where files will be saved. Defaults to system-specific temp directory.
        """
        self.path = path or ("c:/vishal/log" if os.name == 'nt' else '/tmp')
        self.queue = queue.Queue()
        self.worker_thread = Thread(target=self._worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        atexit.register(self.stop)

    def __enter__(self):
        """Enter the runtime context related to this object."""
        return self

    def __exit__(self, exc_type, exc_value, traceback_value):
        """Exit the runtime context related to this object."""
        self.stop()

    def debug_dataframe(self, df_debug: pd.DataFrame, filename: str | None = None, column_formats=None):
        """
        Adds a DataFrame saving task to the queue.

        Parameters:
        df_debug (pd.DataFrame): The DataFrame to be saved.
        filename (str): The name of the file to save the DataFrame to.
        column_formats (dict): Optional. A dictionary specifying column widths for Excel files.
        """
        if not filename:
            filename = "default.xlsx"
        file_extension = os.path.splitext(filename)[1] or '.xlsx'
        file_path = os.path.join(self.path, filename)
        column_formats = column_formats or {}

        self.queue.put((df_debug.copy(), file_path, file_extension, column_formats))

    def _worker(self):

        while True:
            try:
                task = self.queue.get()
                if task is None:
                    break

                df_debug, file_path, file_extension, column_formats = task
                self._save_with_retries(df_debug, file_path, file_extension, column_formats)
                self.queue.task_done()
            except Empty:
                continue

    def _save_with_retries(self, df_debug, file_path, file_extension, column_formats):
        # Convert timezone-aware datetimes to timezone-naive datetimes
        for col in df_debug.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64[ns]']).columns:
            df_debug[col] = df_debug[col].dt.tz_localize(None)

        for attempt in range(self.max_retries):
            try:
                if file_extension == '.csv':
                    self._save_csv(df_debug, file_path)
                elif file_extension == '.xlsx':
                    self._save_excel(df_debug, file_path, column_formats)
                else:
                    raise ValueError("Unsupported file extension")
                return  # Exit if successful
            except PermissionError as e:
                delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                print(f"Attempt {attempt + 1} failed with error: {e}. Retrying in {delay} seconds...")
                time.sleep(delay)

    def stop(self):
        """Stops the worker thread."""
        self.queue.put(None)  # Add a sentinel value to the queue
        self.worker_thread.join()  # Wait for the worker thread to finish

    @staticmethod
    def _save_csv(df: pd.DataFrame, file_path: str):
        """
        Saves a DataFrame to a CSV file.

        Parameters:
        df (pd.DataFrame): The DataFrame to be saved.
        file_path (str): The path to save the CSV file.
        """
        df.to_csv(file_path, index=False, quotechar='"', quoting=csv.QUOTE_MINIMAL)

    @staticmethod
    def _save_excel(df: pd.DataFrame, file_path: str, column_formats):
        """
        Saves a DataFrame to an Excel file with optional column formatting.

        Parameters:
        df (pd.DataFrame): The DataFrame to be saved.
        file_path (str): The path to save the Excel file.
        column_formats (dict): A dictionary specifying column widths.
        """
        with pd.ExcelWriter(file_path, engine="xlsxwriter") as writer:
            df.to_excel(writer, index=False, sheet_name="sheet1")
            worksheet = writer.sheets['sheet1']
            workbook = writer.book
            wrap_format = workbook.add_format({'text_wrap': True})

            column_lengths = {
                col: max(df[col].astype(str).str.len().max(), len(col if isinstance(col, str) else '')) + 2
                for col in df.columns
            }

            for i, col in enumerate(df.columns):
                column_len = column_lengths[col]
                if col in column_formats:
                    worksheet.set_column(
                        i, i, column_formats[col], wrap_format if col in ['clientjira', 'components'] else None
                    )
                else:
                    worksheet.set_column(i, i, column_len)

            row_, col_ = df.shape
            worksheet.autofilter(0, 0, row_, col_ - 1)



# Source https://realpython.com/python-print/
def check_progress(percent=0, width=30):
    left = width * percent // 100
    right = width - left
    print('\r[', '#' * int(left), ' ' * int(right), ']', f' {percent:.1f}%', sep='', end='', flush=True)


def update_progress(
        start, total, time_taken: float = 0, cummulative_time: int = None,
        average_time: float = None
):
    # Source: https://stackoverflow.com/questions/3002085/python-to-print-out-status-bar-and-percentage
    # Modify this to change the length of the progress bar
    bar_length = 30
    status = ""

    if total == 0:
        progress = 0
    else:
        progress = round(start / total, 3)

    if isinstance(progress, int):
        progress = float(progress)

    if not isinstance(progress, float):
        progress = 0
        status = "error: progress var must be float\r\n"

    if progress < 0:
        progress = 0
        status = "Halt...\r\n"

    if progress >= 1:
        progress = 1
        status = "Done...\r\n"
    block = int(round(bar_length * progress))
    # text = ("\rPercent: [{0}] {1}% {2} {3} of {4} processed".
    #         format("=" * block + " " * (barLength - block), round((progress * 100), 3),
    #                status, start, total
    #                )
    #         )
    text_new = (
        f"\r[{'=' * block}{' ' * (bar_length - block)}] "
        f"{round(progress * 100, 3)}% {status} ({start} of {total} processed in {time_taken:.1f} sec)"
    )
    if cummulative_time:
        text_new += f" Total Time: {format_time_difference(cummulative_time)}"
    if average_time:
        text_new += f" Avg. Time: {average_time:.2f} sec"
    sys.stdout.write(text_new)
    sys.stdout.flush()


suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']


def humansize(nbytes):
    i = 0
    while nbytes >= 1024 and i < len(suffixes) - 1:
        nbytes /= 1024.
        i += 1
    f = ('%.2f' % nbytes).rstrip('0').rstrip('.')
    return '%s %s' % (f, suffixes[i])


@inject
def reduce_mem_usage(
        df: pd.DataFrame,
        my_logger=Provide[LoggerContainer.logger]
) -> pd.DataFrame:
    numerics = ['int16', 'int32', 'int64', 'float16', 'float32', 'float64']
    start_mem = df.memory_usage().sum() / 1024 ** 2
    for col in df.columns:
        col_type = df[col].dtypes
        if col_type in numerics:
            c_min = df[col].min()
            c_max = df[col].max()
            # Start addition
            if pd.isnull(df[col]).any():  # Handle NaNs
                continue
            # end addition

            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
                # elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                #     df[col] = df[col].astype(np.int64)

            else:
                # Downcast floats
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
                # else:
                #     df[col] = df[col].astype(np.float64)
        elif col_type == 'object':
            # Check if the column contains lists
            if df[col].apply(lambda x: isinstance(x, list)).any():
                continue  # Skip columns with lists

            if df[col].apply(lambda x: isinstance(x, dict)).any():
                continue  # Skip columns with dict (json)

            # Optimize categorical columns
            num_unique_values = len(df[col].unique())
            num_total_values = len(df[col])
            if num_unique_values / num_total_values < 0.5:
                df[col] = df[col].astype('category')

    end_mem = df.memory_usage().sum() / 1024 ** 2
    my_logger.debug('Memory usage after optimization is: {:.2f} MB'.format(end_mem))
    my_logger.debug('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))

    return df


def cast_columns(df: pd.DataFrame, columns: list[str], dtype: Union[str, pd.Int64Dtype, pd.Float64Dtype]):
    """
    Cast specified columns in the DataFrame to the provided type if they exist.

    Args:
        df (pd.DataFrame): The DataFrame to modify.
        columns (list): List of column names to cast.
        dtype (str or pd.dtype): The type to cast the columns to. Can be 'datetime', 'Int64', 'Float64', etc.

    Returns:
        pd.DataFrame: The modified DataFrame with columns casted as specified.
    """
    # Select the columns that exist in the DataFrame
    existing_columns = [col for col in columns if col in df.columns]

    # Vectorized casting based on dtype
    if isinstance(dtype, (pd.Int64Dtype, pd.Float64Dtype)):
        df[existing_columns] = df[existing_columns].astype(dtype)
    elif dtype == 'datetime':
        df[existing_columns] = df[existing_columns].apply(pd.to_datetime, errors='coerce')
    elif dtype == "date":
        # Convert to datetime first, then extract only the date part
        df[existing_columns] = pd.to_datetime(df[existing_columns], errors='coerce').dt.date
    else:
        df[existing_columns] = df[existing_columns].astype(dtype)
    return df


def schema_check_create(schema_name: str):
    # Define the DDL for creating a schema if it doesn't exist
    create_schema_ddl_template = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_namespace
                WHERE nspname = '%(schema)s'
            ) THEN
                CREATE SCHEMA %(schema)s;
            END IF;
        END $$;
        """

    create_schema_ddl = DDL(create_schema_ddl_template % {'schema': schema_name})

    def create_schema_if_not_exists(target, connection, **kwargs):
        connection.execute(create_schema_ddl, schema=schema_name)

    # Attach the DDL to the Base metadata's before_create event
    event.listen(Base.metadata, 'before_create', create_schema_if_not_exists)

    # event.listen(
    #     Base.metadata,
    #     'before_create',
    #     create_schema_ddl.execute_if(dialect='postgresql')
    # )


# Custom handling for DateTime
class CustomPGDialect(dialect):
    def render_literal_value(self, value, type_):
        if isinstance(type_, DateTime) and isinstance(value, datetime):
            # Convert datetime to ISO 8601 format with timezone awareness
            return f"'{value.isoformat()}'"
        return super().render_literal_value(value, type_)


# Decorator to override the dialect temporarily
def handle_datetime_literals(function):
    @functools.wraps(function)
    def wrapper(query, *args, **kwargs):
        # Use the custom dialect for literal rendering
        return function(query, *args, **kwargs)

    return wrapper


def compile_query(query):
    """Via http://nicolascadou.com/blog/2014/01/printing-actual-sqlalchemy-queries"""
    compiler = query.compile if not hasattr(query, 'statement') else query.statement.compile
    return compiler(dialect=dialect(), compile_kwargs={"literal_binds": False})


@inject
async def detect_deadlocks(
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger=Provide[LoggerContainer.logger]
):
    """
    Detects deadlocks in the PostgreSQL database by analyzing pg_locks and pg_stat_activity.
    Returns a list of PIDs of transactions involved in deadlocks.
    """
    task = asyncio.current_task()
    task.set_name("detect_deadlocks")
    my_logger.info(f"started detect_deadlocks")
    app_container.schema.override('public')

    try:

        async with app_container.database_rw().update_schema('public').async_session() as pg_async_session:

            query = text("""
                WITH blocked_locks AS (
                    SELECT
                        l1.locktype, l1.database, l1.relation, l1.page, l1.tuple,
                        l1.virtualxid, l1.transactionid, l1.classid, l1.objid, l1.objsubid,
                        l1.pid AS blocked_pid, l1.mode AS blocked_mode,
                        l2.pid AS blocking_pid, l2.mode AS blocking_mode
                    FROM
                        pg_locks l1
                    JOIN
                        pg_locks l2
                    ON
                        l1.locktype = l2.locktype
                        AND l1.database IS NOT DISTINCT FROM l2.database
                        AND l1.relation IS NOT DISTINCT FROM l2.relation
                        AND l1.page IS NOT DISTINCT FROM l2.page
                        AND l1.tuple IS NOT DISTINCT FROM l2.tuple
                        AND l1.virtualxid IS NOT DISTINCT FROM l2.virtualxid
                        AND l1.transactionid IS NOT DISTINCT FROM l2.transactionid
                        AND l1.classid IS NOT DISTINCT FROM l2.classid
                        AND l1.objid IS NOT DISTINCT FROM l2.objid
                        AND l1.objsubid IS NOT DISTINCT FROM l2.objsubid
                    WHERE
                        l1.granted = false AND l2.granted = true
                )
                SELECT DISTINCT
                    blocked_pid
                FROM
                    blocked_locks
                WHERE
                    blocked_pid <> blocking_pid;
            """)

            result = await pg_async_session.execute(query)
            rows = [row[0] for row in result.fetchall()]
            my_logger.debug(f"rows returned = {rows}")
            return rows
    except exc.SQLAlchemyError as e:
        my_logger.error(f"Error detecting deadlocks: {e}")
        return []


@inject
async def kill_transaction(
        pid: int,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger=Provide[LoggerContainer.logger]
):
    """
    Kills a transaction in PostgreSQL using pg_terminate_backend.
    """
    task = asyncio.current_task()
    task.set_name("kill_transaction")
    try:
        query = text(f"SELECT pg_terminate_backend(:pid)")
        async with app_container.database_rw().update_schema('public').async_session() as pg_async_session:
            await pg_async_session.execute(query, {'pid': pid})
        my_logger.warning(f"Terminated transaction with PID: {pid}")
    except exc.SQLAlchemyError as e:
        my_logger.error(f"Error terminating transaction {pid}: {e}")


@inject
async def handle_deadlock_with_retry(
        operation_name: str,
        max_deadlock_retries: int = 3,
        deadlock_retry_delay: float = 2.0,
        my_logger=Provide[LoggerContainer.logger]
):
    """
    Enhanced deadlock handler that detects, kills, and provides retry context.

    Args:
        operation_name: Name of the operation for logging
        max_deadlock_retries: Maximum number of deadlock-specific retries
        deadlock_retry_delay: Base delay between deadlock retries
        my_logger: Logger instance

    Returns:
        Dict with deadlock handling results
    """
    try:
        # Detect deadlocked transactions
        deadlocked_pids = await detect_deadlocks()

        if deadlocked_pids:
            my_logger.warning(
                f"Deadlock detected during {operation_name}. "
                f"Found {len(deadlocked_pids)} deadlocked transactions: {deadlocked_pids}"
            )

            # Kill deadlocked transactions
            killed_count = 0
            for pid in deadlocked_pids:
                try:
                    await kill_transaction(pid)
                    killed_count += 1
                except Exception as kill_error:
                    my_logger.error(f"Failed to kill transaction {pid}: {kill_error}")

            my_logger.info(f"Successfully killed {killed_count}/{len(deadlocked_pids)} deadlocked transactions")

            return {
                'deadlocks_found': len(deadlocked_pids),
                'deadlocks_killed': killed_count,
                'success': killed_count > 0
            }
        else:
            my_logger.debug(f"No deadlocks detected during {operation_name}")
            return {
                'deadlocks_found': 0,
                'deadlocks_killed': 0,
                'success': True
            }

    except Exception as e:
        my_logger.error(f"Error in deadlock handling for {operation_name}: {e}")
        return {
            'deadlocks_found': 0,
            'deadlocks_killed': 0,
            'success': False,
            'error': str(e)
        }


@inject
async def monitor_deadlocks(
        interval: int = 5,
        my_logger=Provide[LoggerContainer.logger]

):
    """
    Monitors and resolves deadlocks periodically using the enhanced deadlock handler.
    """
    task = asyncio.current_task()
    task.set_name("monitor_deadlocks")
    my_logger.info("Started periodic deadlock monitoring")

    while True:
        try:
            result = await handle_deadlock_with_retry("periodic_monitoring")
            if result['deadlocks_found'] > 0:
                my_logger.info(f"Periodic deadlock check result: {result}")
            await asyncio.sleep(interval)
        except asyncio.CancelledError:
            my_logger.info("Deadlock monitoring cancelled")
            break
        except Exception as e:
            my_logger.error(f"Error in deadlock monitoring: {e}")
            await asyncio.sleep(interval)


def deadlock_retry(
        max_retries: int = 3,
        base_delay: float = 2.0,
        max_delay: float = 30.0,
        enable_deadlock_killing: bool = True
):
    """
    Decorator that automatically handles deadlocks with retry logic and optional deadlock killing.

    Args:
        max_retries: Maximum number of retry attempts for deadlock errors
        base_delay: Base delay between retries (exponential backoff applied)
        max_delay: Maximum delay between retries
        enable_deadlock_killing: Whether to attempt killing deadlocked transactions
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_error = None

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)

                except (DeadlockDetectedError, DeadlockDetected) as e:
                    last_error = e
                    attempt_num = attempt + 1

                    # Get logger from kwargs or use default
                    logger = kwargs.get('my_logger') or logging.getLogger(__name__)

                    logger.warning(
                        f"Deadlock detected in {func.__name__} (attempt {attempt_num}/{max_retries}): {e}"
                    )

                    if attempt < max_retries - 1:  # Not the last attempt
                        if enable_deadlock_killing:
                            # Try to resolve deadlock
                            deadlock_result = await handle_deadlock_with_retry(func.__name__)
                            if deadlock_result['deadlocks_found'] > 0:
                                logger.info(f"Deadlock resolution: {deadlock_result}")

                        # Calculate delay with exponential backoff
                        delay = min(base_delay * (2 ** attempt), max_delay)
                        logger.info(f"Retrying {func.__name__} in {delay:.2f} seconds...")
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"Max deadlock retries ({max_retries}) exceeded for {func.__name__}")
                        break

                except Exception as e:
                    # For non-deadlock errors, re-raise immediately
                    raise

            # All retries exhausted
            raise MaxRetriesExceededError(
                f"Function {func.__name__} failed after {max_retries} deadlock retry attempts"
            ) from last_error

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_error = None

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)

                except DeadlockDetected as e:  # Only sync deadlock for sync functions
                    last_error = e
                    attempt_num = attempt + 1

                    # Get logger from kwargs or use default
                    logger = kwargs.get('my_logger') or logging.getLogger(__name__)

                    logger.warning(
                        f"Deadlock detected in {func.__name__} (attempt {attempt_num}/{max_retries}): {e}"
                    )

                    if attempt < max_retries - 1:  # Not the last attempt
                        # Calculate delay with exponential backoff
                        delay = min(base_delay * (2 ** attempt), max_delay)
                        logger.info(f"Retrying {func.__name__} in {delay:.2f} seconds...")
                        time.sleep(delay)
                    else:
                        logger.error(f"Max deadlock retries ({max_retries}) exceeded for {func.__name__}")
                        break

                except Exception as e:
                    # For non-deadlock errors, re-raise immediately
                    raise

            # All retries exhausted
            raise MaxRetriesExceededError(
                f"Function {func.__name__} failed after {max_retries} deadlock retry attempts"
            ) from last_error

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


@inject
def smart_retry(
        min_wait=5, max_wait=10, max_delay=60, max_retries=5,
        my_logger=Provide[LoggerContainer.logger]
):
    # Validate input arguments
    if min_wait <= 0 or max_wait <= 0 or max_delay <= 0:
        raise ValueError("Wait times and max_delay must be positive numbers.")
    if min_wait > max_wait:
        raise ValueError("min_wait cannot be greater than max_wait.")
    if max_retries <= 0:
        raise ValueError("max_retries must be a positive integer.")
    if max_delay < min_wait:
        raise ValueError("max_delay must be greater than or equal to min_wait.")

    def decorator(func_local):
        @functools.wraps(func_local)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            async def async_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return await func_local(*args, **kwargs)  # Await for async functions
                    except DeadlockDetectedError as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Async retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        await asyncio.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            def sync_retry_logic():
                retries = 0
                while retries < max_retries:
                    try:
                        return func_local(*args, **kwargs)  # Direct call for sync functions
                    except DeadlockDetected as e:
                        retries += 1
                        elapsed_time = (time.time() - start_time)
                        if elapsed_time >= max_delay:
                            raise TimeoutError(
                                f"Retry limit of {max_delay} seconds reached after {retries} attempts."
                            ) from e
                        wait_time = min(max_wait, min_wait * (2 ** retries))
                        if isinstance(my_logger, Logger):
                            my_logger.warning(
                                f"Sync retry {retries}/{max_retries} in {wait_time:.2f} seconds due to: {e}"
                            )
                        time.sleep(wait_time)
                raise TimeoutError("Max retries reached without success.")

            # Determine whether the function is async or sync
            if asyncio.iscoroutinefunction(func_local):
                return async_retry_logic()
            else:
                return sync_retry_logic()

        return wrapper

    return decorator


# Helper to chunk rows into batches
def chunk_rows(rows, batch_size):
    iterator = iter(rows)
    while chunk := list(islice(iterator, batch_size)):
        yield chunk


# Foreign key constraints handler
def handle_foreignkeys_constraints(row, foreign_keys):
    for c_name, c_value in foreign_keys.items():
        foreign_obj = row.pop(c_value.table.name, None)
        if foreign_obj:
            row[c_name] = getattr(foreign_obj, c_value.name)
        else:
            row[c_name] = row.get(c_name)
    return row


# Unique constraints validator
@inject
def validate_unique_constraints(
        row, unique_constraints, seen, conflict_keys,
        my_logger=Provide[LoggerContainer.logger]
):
    """
    Validate and track unique constraints for a row.

    Parameters:
        row: Dictionary representing a row of data.
        unique_constraints: List of unique constraints for the table.
        seen: Set of already processed unique keys.
        conflict_keys: Set to track rows with unique constraint conflicts.
        my_logger: Logger instance for logging.

    Returns:
        Processed row or None if invalid.
    """
    for const in unique_constraints:
        unique_key = tuple((col.name, row[col.name]) for col in const.columns)
        if unique_key in seen:
            conflict_keys.add(unique_key)
            my_logger.warning(f"Conflict detected for unique constraint: {unique_key}. Adding to conflict resolution.")
        seen.add(unique_key)
    return row


# Build the conflict condition
def build_conflict_condition(model):
    return and_(
        model.initiative_id == insert(model.__table__).excluded.initiative_id,
        model.epic_id == insert(model.__table__).excluded.epic_id,
        model.standard_id == insert(model.__table__).excluded.standard_id,
        model.subtask_id == insert(model.__table__).excluded.subtask_id,
    )


# Upsert function
@smart_retry(min_wait=2, max_wait=5, max_retries=3)
@inject
def upsert(
        session: Session,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        batch_size: int | None = None,
        my_logger=Provide[LoggerContainer.logger],
) -> None:
    """
    Perform an upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Parameters:
        session: SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        batch_size: Maximum number of rows per batch.
        my_logger: Logger instance for error logging.

    Returns:
        None
    """
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    # https://gist.github.com/bhtucker/c40578a2fb3ca50b324e42ef9dce58e1
    if rows.shape[0] == 0:
        return

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}
    # index_where = and_(*conflict_condition) if conflict_condition else None
    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            # index_where=index_where,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        return

    # Calculate max rows per batch based on PostgresSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    try:
        for batch in chunk_rows(processed_rows, batch_size):
            session.execute(stmt, batch)

    except Exception as e:
        my_logger.error(f"Upsert failed with error: {e}.", exc_info=True)
        handle_exception(e)


# Enhanced error types for better error handling
class DatabaseConnectionError(Exception):
    """Raised when database connection fails"""
    pass

class MaxRetriesExceededError(Exception):
    """Raised when maximum retry attempts are exhausted"""
    pass

class GracefulShutdownRequested(Exception):
    """Raised when graceful shutdown is requested due to critical errors"""
    pass

# Global shutdown flag
_shutdown_requested = False

def request_graceful_shutdown(reason: str, logger: Logger):
    """Request graceful shutdown of the process"""
    global _shutdown_requested
    if not _shutdown_requested:
        _shutdown_requested = True
        logger.critical(f"GRACEFUL SHUTDOWN REQUESTED: {reason}")
        logger.critical("Process will terminate to prevent data corruption")
        # You can add additional shutdown logic here, such as:
        # - Sending notifications
        # - Saving state
        # - Cleaning up resources
    raise GracefulShutdownRequested(reason)

def is_shutdown_requested() -> bool:
    """Check if graceful shutdown has been requested"""
    return _shutdown_requested

# Async upsert function

@debug_async_function("upsert_async")
@inject
async def upsert_async(
        session: AsyncSession,
        model: Type[Base],
        rows: pd.DataFrame,
        no_update_cols: tuple[str, ...] = (),
        on_conflict_update: bool = True,
        conflict_condition: list | BinaryExpression | BooleanClauseList | None = None,
        batch_size: int | None = None,
        max_retries=5,
        retry_delay=1.0,
        enable_graceful_shutdown=True,
        my_logger=Provide[LoggerContainer.logger],
) -> None:
    """
    Perform an async upsert (insert or update on conflict) operation on the given SQLAlchemy model.

    Enhanced with comprehensive error handling, retry logic, and graceful shutdown capabilities.

    Parameters:
        session: Async SQLAlchemy session.
        model: SQLAlchemy model class.
        rows: DataFrame containing rows to upsert.
        no_update_cols: Columns that should not be updated on conflict.
        on_conflict_update: Whether to perform updates on conflict.
        conflict_condition: Additional conditions for conflict resolution.
        batch_size: Maximum number of rows per batch.
        max_retries: Maximum number of retry attempts for connection errors.
        retry_delay: Base delay between retries (exponential backoff applied).
        enable_graceful_shutdown: Whether to trigger graceful shutdown on max retries.
        my_logger: Logger instance for error logging.

    Returns:
        None

    Raises:
        GracefulShutdownRequested: When max retries exceeded and graceful shutdown enabled.
        DatabaseConnectionError: When database connection cannot be established.
        MaxRetriesExceededError: When max retries exceeded but graceful shutdown disabled.
    """
    # Check if shutdown was already requested
    if is_shutdown_requested():
        raise GracefulShutdownRequested("Shutdown already requested")

    asyncio.current_task().set_name("upsert_async")

    if rows.shape[0] == 0:
        return

    table_name = model.__table__.name
    my_logger.debug(f"Starting upsert for table {table_name} with {rows.shape[0]} rows")

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    table = model.__table__
    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]
    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}

    if conflict_condition is not None:
        if isinstance(conflict_condition, list):
            index_where = and_(
                *(getattr(model, col) < getattr(stmt.excluded, col) for col in conflict_condition)
            )
        else:
            index_where = conflict_condition
    else:
        index_where = None

    if on_conflict_update:
        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            where=index_where,
        )
    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)

    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    seen = set()
    conflict_keys = set()
    processed_rows = []
    for row in rows.to_dict(orient="records"):
        row = handle_foreignkeys_constraints(row, foreign_keys)
        row = validate_unique_constraints(row, unique_constraints, seen, conflict_keys)
        if row:
            processed_rows.append(row)

    if not processed_rows:
        my_logger.debug(f"No rows to process for table {table_name} after validation")
        return

    # Calculate max rows per batch based on PostgreSQL parameter limit
    num_columns = len(processed_rows[0]) if processed_rows else 0
    max_params = 65535
    max_rows_per_batch = max_params // num_columns if num_columns else 1

    if batch_size is None:
        batch_size = max_rows_per_batch
    elif batch_size > max_rows_per_batch:
        batch_size = max_rows_per_batch

    my_logger.debug(f"Processing {len(processed_rows)} rows in batches of {batch_size}")

    last_error = None
    for attempt in range(max_retries):
        try:
            batch_count = 0
            for batch in chunk_rows(processed_rows, batch_size):
                await session.execute(stmt, batch)
                batch_count += 1

            my_logger.debug(f"Successfully processed {batch_count} batches for table {table_name}")
            return

        except Exception as e:
            last_error = e
            attempt_num = attempt + 1

            # Enhanced error classification
            error_msg = str(e).lower()
            error_type = type(e).__name__

            # Check for deadlock errors (both sync and async)
            is_deadlock_error = (
                isinstance(e, (DeadlockDetectedError, DeadlockDetected)) or
                'deadlock' in error_msg or
                'DeadlockDetectedError' in error_type or
                'DeadlockDetected' in error_type
            )

            # Check for failed transaction errors
            is_failed_transaction_error = (
                'current transaction is aborted' in error_msg or
                'infailedsqltransactionerror' in error_msg or
                'InFailedSQLTransactionError' in error_type
            )

            # Check for lock timeout and availability errors
            is_lock_error = (
                'LockNotAvailableError' in error_type or
                'locknotavailableerror' in error_msg or
                'lock timeout' in error_msg or
                'canceling statement due to lock timeout' in error_msg or
                'could not obtain lock' in error_msg
            )

            # Check for concurrent operation errors
            is_concurrent_operation_error = (
                'another operation is in progress' in error_msg or
                'interfaceerror' in error_msg or
                'cannot perform operation' in error_msg
            )

            # Check for various types of connection and timeout errors
            is_connection_error = (
                isinstance(e, (InterfaceError, DisconnectionError)) or
                'asyncio.exceptions.CancelledError' in str(type(e)) or
                'TimeoutError' in error_type or
                'CancelledError' in error_type or
                any(msg in error_msg for msg in [
                    'connection is closed',
                    'connection was closed',
                    'connection terminated',
                    'server closed the connection',
                    'connection reset',
                    'connection refused',
                    'timeout',
                    'cancelled',
                    'network is unreachable',
                    'host is unreachable'
                ])
            )

            # Log detailed error information
            my_logger.error(
                f"Upsert attempt {attempt_num}/{max_retries} failed for table {table_name}: "
                f"{error_type}: {str(e)[:200]}..."
            )

            # Handle deadlock errors with automatic deadlock detection and killing
            if is_deadlock_error and attempt < max_retries - 1:
                my_logger.warning(f"Deadlock detected in upsert for table {table_name}. Attempting to resolve...")

                # Use the enhanced deadlock handler
                deadlock_result = await handle_deadlock_with_retry(f"upsert_{table_name}")

                if deadlock_result['success']:
                    if deadlock_result['deadlocks_found'] > 0:
                        my_logger.info(
                            f"Deadlock resolution successful: killed {deadlock_result['deadlocks_killed']}/"
                            f"{deadlock_result['deadlocks_found']} deadlocked transactions"
                        )
                    else:
                        my_logger.info("No active deadlocks found, deadlock may have resolved naturally")
                else:
                    my_logger.warning(f"Deadlock resolution failed: {deadlock_result.get('error', 'Unknown error')}")

                # Wait before retry with exponential backoff (slightly longer for deadlocks)
                wait_time = retry_delay * (2 ** attempt) + (retry_delay * 0.2 * attempt)
                my_logger.warning(
                    f"Deadlock error detected. Retrying in {wait_time:.2f} seconds... "
                    f"(attempt {attempt_num}/{max_retries})"
                )
                await asyncio.sleep(wait_time)
                continue

            elif is_connection_error and attempt < max_retries - 1:
                # Exponential backoff with jitter
                wait_time = retry_delay * (2 ** attempt) + (retry_delay * 0.1 * attempt)
                my_logger.warning(
                    f"Connection error detected. Retrying in {wait_time:.2f} seconds... "
                    f"(attempt {attempt_num}/{max_retries})"
                )
                await asyncio.sleep(wait_time)
                continue

            elif is_failed_transaction_error and attempt < max_retries - 1:
                # Handle failed transaction state - rollback and retry
                my_logger.warning(f"Failed transaction detected in upsert for table {table_name}. Attempting rollback...")
                try:
                    await session.rollback()
                    my_logger.info(f"Successfully rolled back failed transaction for table {table_name}")

                    # Brief wait before retry
                    wait_time = retry_delay * (1.5 ** attempt)
                    my_logger.warning(
                        f"Failed transaction rolled back. Retrying in {wait_time:.2f} seconds... "
                        f"(attempt {attempt_num}/{max_retries})"
                    )
                    await asyncio.sleep(wait_time)
                    continue

                except Exception as rollback_error:
                    my_logger.error(f"Failed to rollback transaction for table {table_name}: {rollback_error}")
                    # If rollback fails, this is a critical error - don't retry
                    raise DatabaseConnectionError(f"Transaction rollback failed: {rollback_error}") from e

            elif is_lock_error and attempt < max_retries - 1:
                # Handle lock timeout errors with longer backoff
                wait_time = retry_delay * (3 ** attempt) + random.uniform(1, 5)  # Longer wait + jitter
                my_logger.warning(
                    f"Lock timeout detected for table {table_name}. Retrying in {wait_time:.2f} seconds... "
                    f"(attempt {attempt_num}/{max_retries})"
                )
                await asyncio.sleep(wait_time)
                continue

            elif is_concurrent_operation_error and attempt < max_retries - 1:
                # Handle concurrent operation errors with brief wait
                wait_time = retry_delay * (2 ** attempt) + random.uniform(0.5, 2.0)  # Moderate wait + jitter
                my_logger.warning(
                    f"Concurrent operation detected for table {table_name}. Retrying in {wait_time:.2f} seconds... "
                    f"(attempt {attempt_num}/{max_retries})"
                )
                await asyncio.sleep(wait_time)
                continue

            elif not (is_connection_error or is_deadlock_error or is_failed_transaction_error or
                     is_lock_error or is_concurrent_operation_error):
                # For non-retryable errors, fail immediately
                my_logger.error(f"Non-retryable error in upsert for table {table_name}: {e}", exc_info=True)
                raise
            else:
                # Max retries reached for retryable error
                break

    # All retries exhausted
    error_summary = (
        f"All {max_retries} retry attempts exhausted for upsert operation on table {table_name}. "
        f"Last error: {type(last_error).__name__}: {str(last_error)[:200]}..."
    )

    my_logger.critical(error_summary)

    if enable_graceful_shutdown:
        # Request graceful shutdown to prevent data corruption
        request_graceful_shutdown(
            f"Database upsert failed after {max_retries} retries for table {table_name}",
            my_logger
        )
    else:
        # Raise custom exception instead of the original error
        raise MaxRetriesExceededError(error_summary) from last_error

async def check_session_health(session: AsyncSession, my_logger: Logger) -> bool:
    """
    Check if the database session is healthy and can execute queries.

    This function also detects failed transaction states and attempts recovery.

    Returns:
        bool: True if session is healthy, False otherwise
    """
    try:
        # Simple query to test connection
        result = await session.execute(text("SELECT 1"))
        result.fetchone()
        return True
    except Exception as e:
        error_msg = str(e).lower()

        # Check for failed transaction state specifically
        if 'current transaction is aborted' in error_msg or 'infailedsqltransactionerror' in error_msg:
            my_logger.warning(f"Session in failed transaction state, attempting rollback: {e}")
            try:
                await session.rollback()
                my_logger.info("Successfully rolled back failed transaction")
                # Test again after rollback
                result = await session.execute(text("SELECT 1"))
                result.fetchone()
                my_logger.info("Session health restored after rollback")
                return True
            except Exception as rollback_error:
                my_logger.error(f"Failed to recover session after rollback: {rollback_error}")
                return False
        else:
            my_logger.warning(f"Session health check failed: {e}")
            return False

async def upsert_async_with_health_check(
        session: AsyncSession,
        model: Type[Base],
        rows: pd.DataFrame,
        **kwargs
) -> None:
    """
    Wrapper around upsert_async that includes session health checks and graceful error handling.

    This function handles transaction recovery and provides robust error handling for database operations.
    """
    my_logger = kwargs.get('my_logger') or logging.getLogger(__name__)
    max_health_check_retries = 2

    for health_attempt in range(max_health_check_retries):
        try:
            # Check session health before attempting upsert
            if not await check_session_health(session, my_logger):
                if health_attempt < max_health_check_retries - 1:
                    my_logger.warning(f"Session health check failed, retrying... (attempt {health_attempt + 1}/{max_health_check_retries})")
                    await asyncio.sleep(0.5)  # Brief pause before retry
                    continue
                else:
                    raise DatabaseConnectionError("Session health check failed after all retries")

            # Perform the upsert operation
            await upsert_async(session, model, rows, **kwargs)
            return  # Success, exit the retry loop

        except Exception as e:
            error_msg = str(e).lower()

            # Handle failed transaction errors specifically
            if 'current transaction is aborted' in error_msg or 'infailedsqltransactionerror' in error_msg:
                my_logger.warning(f"Failed transaction detected during upsert, attempting recovery: {e}")

                if health_attempt < max_health_check_retries - 1:
                    try:
                        await session.rollback()
                        my_logger.info("Transaction rolled back successfully, retrying upsert")
                        await asyncio.sleep(0.5)  # Brief pause before retry
                        continue
                    except Exception as rollback_error:
                        my_logger.error(f"Failed to rollback transaction: {rollback_error}")
                        raise DatabaseConnectionError(f"Transaction recovery failed: {rollback_error}") from e
                else:
                    my_logger.error("Failed transaction recovery exhausted all retries")
                    raise DatabaseConnectionError(f"Failed transaction could not be recovered: {e}") from e
            else:
                # For other errors, re-raise immediately
                raise

    # If we reach here, all health check retries were exhausted
    raise DatabaseConnectionError("All health check retries exhausted")

async def delete_in_batches(session: AsyncSession, ids_to_delete, batch_size: int = 32_000):
    # Split the IDs into smaller chunks
    for i in range(0, len(ids_to_delete), batch_size):
        chunk = ids_to_delete[i:i + batch_size]
        delete_stmt = delete(WorkLog).where(WorkLog.id.in_(chunk))
        await session.execute(delete_stmt)
    await session.commit()

@inject
async def get_jira_users(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:
    return_string = ""
    try:
        # config_dict = get_env_variables()
        task = asyncio.current_task()
        task.set_name("get_jira_users")

        tasks = []
        endpoint = "/rest/api/3/users"
        url = f"{jira_entry.url}{endpoint}"
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(
                headers=jira_entry.custom_properties,
            timeout=timeout
        ) as http_session:
            for i in range(3):
                payload_dict = {'startAt': i * 1000, 'maxResults': 1000}
                tasks.append(fetch_with_retries_get_new(http_session, url, payload_dict))
            responses = await asyncio.gather(*tasks)

        # Separate successes and exceptions
        successful_responses = [resp["result"] for resp in responses if resp.get("success")]
        exceptions = [resp["exception"] for resp in responses if not resp.get("success")]
        my_logger.debug(f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}")
        my_logger.debug(f"# of exceptions: {len(exceptions)}")

        if not exceptions:
            # iterate through the sublist using List comprehension
            responses = [element for innerList in successful_responses for element in innerList]

            df = pd.DataFrame(responses)
            df.drop(columns=["self", "avatarUrls"])
            return_string = f"+{df.shape[0]}"

            app_container.schema.override('public')
            with app_container.database_rw().session() as pg_session:
                try:
                    upsert(
                        pg_session, User, df,
                        on_conflict_update=True
                    )
                    pg_session.commit()
                except ConnectionResetError as e:
                    my_logger.error(f"Database connection was reset: {e}")
                    return_string = "DB EXCEPTION"
                    raise e
        else:
            # Log or handle exceptions
            for exception in exceptions:
                my_logger.error(f"Task failed with exception: {exception}")
            return_string = "UNKNOWN EXCEPTION"
    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)
        raise e

    return return_string


@inject
async def get_all_jira_boards(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    ret_string = ""
    my_logger.debug("Started get_all_jira_boards")
    tasks = []
    asyncio.current_task().set_name("get_all_jira_boards")

    endpoint = "/rest/agile/1.0/board/"
    url = f"{jira_entry.url}{endpoint}"
    app_container.schema.override('public')
    my_logger.debug(f"schema is set to: {app_container.schema()}")
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(headers=jira_entry.custom_properties, timeout=timeout) as http_session:
        for i in range(2):
            payload_dict = {'startAt': i * 50, 'maxResults': 50}
            tasks.append(fetch_with_retries_get_new(http_session, url, payload_dict))
        responses = await asyncio.gather(*tasks)

        # Separate successes and exceptions
        successful_responses = [resp["result"] for resp in responses if resp.get("success")]
        exceptions = [resp["exception"] for resp in responses if not resp.get("success")]
        my_logger.debug(f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}")
        my_logger.debug(f"# of exceptions: {len(exceptions)}")

        if not exceptions:

            with DataframeDebugger() as debugger:
                debug_dataframe = debugger.debug_dataframe
                # df = pd.DataFrame(responses)
                df = pd.DataFrame(successful_responses)
                df = df[df['values'].apply(lambda x: len(x) > 0)]
                debug_dataframe(df, f"df_initial_all_boards.xlsx")
                # Drop rows where 'values' column is an empty list

                df = df.explode(column="values").reset_index()
                df = pd.json_normalize(df["values"])
                df.drop(columns=["self", "location.name", "location.avatarURI"], inplace=True)
                df.rename(columns=lambda x: x.replace('location.', ''), inplace=True)
                return_string = f"+{df.shape[0]}"

                df['projectId'] = df['projectId'].astype(pd.Int64Dtype())
                df['userId'] = df['userId'].astype(pd.Int64Dtype())

                ret_string = f"+{df.shape[0]}"

            async with app_container.database_rw().async_session() as pg_async_session:
                try:
                    await upsert_async(pg_async_session, AllBoards, df)
                    await pg_async_session.commit()
                except Exception as e:
                    handle_exception(e)
            return return_string
        else:
            # Log or handle exceptions
            for exception in exceptions:
                my_logger.error(f"Task failed with exception: {exception}")
            return "UNKNOWN Exception"





########
@inject
async def get_deleted_worklog(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    return_string = ""
    try:
        task = asyncio.current_task()
        task.set_name("deleted_worklogs")
        endpoint = "/rest/api/3/myself"
        url = f"{jira_entry.url}{endpoint}"
        my_logger.debug(f"module name = {__name__}")
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(
                headers=jira_entry.custom_properties, timeout=timeout
        ) as http_session:
            response = await fetch_with_retries_get_new(http_session, url, my_logger=my_logger)
            my_logger.debug(f"{response}")
            time_zone = ZoneInfo(response['result']['timeZone'])
            # Set local_datetime to current time
            last_run_time = datetime.now(tz=time_zone)

            async with app_container.database_rw().async_session() as pg_async_session:
                stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "DeletedWorklog")
                try:
                    result = await pg_async_session.execute(stmt)
                    local_datetime = result.scalar_one()
                    my_logger.debug(f"local datetime returned: {local_datetime}")
                except sqlalchemy.exc.NoResultFound:
                    # Create a datetime object with the specified date, time, and timezone
                    local_datetime = datetime(
                        year=2010, month=1, day=1, hour=0, minute=0, second=0, tzinfo=time_zone
                    )
                    my_logger.info(f"no rows found in {RunDetailsJira.__table__}. default time: {local_datetime}")

                since = int(
                    (
                            local_datetime - datetime(1970, 1, 1, tzinfo=time_zone)
                    ).total_seconds() * 1000)
                last_page = False

                endpoint = "/rest/api/3/worklog/deleted"
                url = f"{jira_entry.url}{endpoint}"

                page_number = 0
                params = {'since': since}
                while not last_page:
                    my_logger.debug(f"page number {(page_number := page_number + 1)}")
                    response = await fetch_with_retries_get_new(http_session, url, params=params)
                    if response.get("success"):
                        last_page = response['result']['lastPage']
                        df = pd.DataFrame(response['result']['values'])

                    if not last_page:
                        url = response['result']['nextPage']
                    params = None

                    await upsert_async(
                        pg_async_session, DeletedWorklog, df, on_conflict_update=False
                    )

                run_details_jira = RunDetailsJira(topic="DeletedWorklog", last_run=last_run_time)
                await pg_async_session.merge(run_details_jira)
                await pg_async_session.commit()

    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)

    return return_string


########
@inject
async def delete_worklog(
        project_key: str,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    return_string = ""
    try:
        task = asyncio.current_task()
        task.set_name("delete_worklog")

        # Aliases for models
        worklog_alias = aliased(WorkLog)
        deleted_worklog_alias = aliased(DeletedWorklog)
        # Create the subquery to identify matching rows
        # subquery = (
        #     select(worklog_alias.id.label('worklog_id'))
        #     .join(deleted_worklog_alias, worklog_alias.id == deleted_worklog_alias.worklogId)
        #     .subquery()
        # )
        cte_query = (
            select(worklog_alias.id.label('worklog_id'))
            .join(deleted_worklog_alias, worklog_alias.id == deleted_worklog_alias.worklogId)
            .cte('matching_worklogs')
        )

        # Create the delete query using the subquery
        delete_query = (
            delete(WorkLog)
            .where(WorkLog.id.in_(select(cte_query.c.worklog_id)))
            .returning(column('xmax'))
        )
        my_logger.debug(f"{compile_query(delete_query)}")
        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            async with pg_async_session.begin():
                result = await pg_async_session.execute(delete_query.execution_options(synchronize_session='fetch'))
                deleted_count = result.rowcount
                await pg_async_session.commit()
        my_logger.debug(f"{project_key} deleted count = {deleted_count}")

        # async with aiohttp.ClientSession(headers=jira_entry.custom_properties) as http_session:
        #     response = await fetch_with_retries_get_new(http_session, url, my_logger=my_logger)
        #     time_zone = ZoneInfo(response['success']['timeZone'])
        #
        #     async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
        #         delete_stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Worklog")
        #         try:
        #             result = await pg_async_session.execute(delete_stmt)
        #             local_datetime = result.scalar_one()
        #             my_logger.debug(f"local datetime returned: {local_datetime}")
        #         except sqlalchemy.exc.NoResultFound:
        #             # Create a datetime object with the specified date, time, and timezone
        #             local_datetime = datetime(
        #                 year=2010, month=1, day=1, hour=0, minute=0, second=0, tzinfo=time_zone
        #             )
        #             my_logger.info(f"no rows found in {RunDetailsJira.__table__}. default time: {local_datetime}")
        #     since = int((local_datetime - datetime(1970, 1, 1, tzinfo=time_zone)).total_seconds() * 1000)
        #     last_page = False
        #     wkid_master: list = []
        #
        #     endpoint = "/rest/api/3/worklog/deleted"
        #     url = f"{jira_entry.url}{endpoint}"
        #
        #     page_number = 0
        #     params = {'since': since}
        #     while not last_page:
        #         my_logger.debug(f"page number {(page_number := page_number + 1)}")
        #         response = await fetch_with_retries_get_new(http_session, url, params=params)
        #
        #         last_page = response['success']['lastPage']
        #
        #         wkid = [item["worklogId"] for item in response["values"]]
        #         if len(wkid) > 0:
        #             wkid_master.extend(wkid)
        #
        #         # params = {'since': urlparse(response.get('nextPage', '')).query if not last_page else None}
        #
        #         # 1 liner
        #         # params = {
        #         # 'since': parse_qs(urlparse(response.get('nextPage', '')).query).get('since', [None])[0]
        #         # if not last_page else None
        #         # }
        #
        #         if not last_page:
        #             url = response['nextPage']
        #             # parsed_url = urlparse(response.get('nextPage', ''))
        #             # my_logger.debug(f"parsed_url = {parsed_url}")
        #             # my_logger.debug(f"parsed_url = {parsed_url.query}")
        #             # captured_value = parse_qs(parsed_url.query)['since'][0]
        #             # params = {'since': captured_value}
        #         params = None
        #
        #     my_logger.debug(f"Count of deleted worklogs: {len(wkid_master)}")
        #
        #     async with app_container.database_rw().async_session() as pg_async_session:
        #         if len(wkid_master) > 32_767:
        #             await delete_in_batches(pg_async_session, wkid_master)
        #
        #             # value_expr = values(
        #             #     column('id', Integer),
        #             #     name="delete_worklog_ids"
        #             # ).data([(wk_id,) for wk_id in wkid_master])
        #             #
        #             # # Step 2: Execute the select to get the list of IDs
        #             # result = await pg_session.execute(select(value_expr.c.id))
        #             # ids_to_delete = [row[0] for row in result.fetchall()]
        #
        #         else:
        #             select_stmt = select(WorkLog.id).where(WorkLog.id.in_(wkid_master))
        #             result = await pg_async_session.execute(select_stmt)
        #             deleted_ids = [row[0] for row in result.fetchall()]  # Extract the IDs
        #
        #             delete_stmt = WorkLog.__table__.delete().where(WorkLog.id.in_(wkid_master))
        #             result = await pg_async_session.execute(delete_stmt)
        #             deleted_count = result.rowcount
        #
        #         # Set local_datetime to current time
        #         local_datetime = datetime.now(tz=time_zone)
        #
        #         run_details_jira = RunDetailsJira(topic="Worklog", last_run=local_datetime)
        #         await pg_async_session.merge(run_details_jira)
        #         await pg_async_session.commit()

    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)
    finally:
        return return_string


@inject
async def get_fields(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:
    task = asyncio.current_task()
    task.set_name("get_fields")
    ret_value = "Not Initialized"
    try:
        endpoint = "/rest/api/3/field"
        url = f"{jira_entry.url}{endpoint}"
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(
                headers=jira_entry.custom_properties, timeout=timeout
        ) as http_session:
            response = await fetch_with_retries_get_new(http_session, url)

            for node in response["result"]:
                for key, value in node.items():
                    if key == 'schema':
                        node['schema'] = [node['schema']]

            df = pd.json_normalize(response["result"])
            df['schema'] = df['schema'].apply(lambda x: x[0] if isinstance(x, list) and len(x) > 0 else {})
        ret_value = f"+{df.shape[0]}"
        my_logger.info(f"Fields returned: {df.shape[0]}")

        app_container.schema.override('public')
        with app_container.database_rw().session() as pg_session:
            upsert(pg_session, IssueFields, df, )
            pg_session.commit()
            my_logger.debug("DB commit done!!!")
    except Exception as e:
        ret_value = "FAILED"
        handle_exception(e)
    finally:
        return ret_value




# Configuration and Data Classes
@dataclass
class SprintFetchConfig:
    max_results_per_page: int = 50
    max_concurrent_requests: int = 5
    max_retries: int = 5

@dataclass
class SprintFetchResult:
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[Exception] = None
    sprint_id: Optional[int] = None
    request_type: str = "unknown"

# Protocols (Interfaces)
class HttpClientProtocol(Protocol):
    async def fetch_with_retries(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        ...

class DataProcessorProtocol(Protocol):
    def process_sprint_data(self, responses: List[Dict[str, Any]]) -> pd.DataFrame:
        ...


# Queue-based HTTP Client that leverages your existing retry logic
class QueuedHttpClient:
    def __init__(self, session: aiohttp.ClientSession, config: SprintFetchConfig):
        self.session = session
        self.config = config
        self.request_queue = asyncio.Queue()
        self.response_queue = asyncio.Queue()
        self._workers_started = False
        self._active_workers = 0

    async def start_workers(self):
        """Start worker tasks for processing requests"""
        if self._workers_started:
            return

        for worker_id in range(self.config.max_concurrent_requests):
            asyncio.create_task(self._worker(worker_id))
        self._workers_started = True

    async def _worker(self, worker_id: int):
        """Worker coroutine that processes requests from the queue using your existing retry logic"""
        request_data = None
        while True:
            try:
                self._active_workers += 1
                request_data = await self.request_queue.get()

                if request_data is None:  # Sentinel to stop worker
                    break

                # Use your existing fetch_with_retries function
                result = await fetch_with_retries(
                    session=self.session,
                    method=request_data.get('method', 'GET'),
                    url=request_data['url'],
                    params=request_data.get('params'),
                    json_payload=request_data.get('json_payload'),
                    retries=self.config.max_retries
                )

                # Transform result to our format
                fetch_result = SprintFetchResult(
                    success=result.get('success', False) if result else False,
                    data=result.get('result') if result and result.get('success') else None,
                    error=Exception(result.get('exception', 'Unknown error')) if result and not result.get(
                        'success') else None,
                    sprint_id=request_data.get('sprint_id'),
                    request_type=request_data.get('request_type', 'unknown')
                )

                await self.response_queue.put(fetch_result)

            except Exception as e:
                # Handle any unexpected errors
                await self.response_queue.put(SprintFetchResult(
                    success=False,
                    error=e,
                    sprint_id=request_data.get('sprint_id') if 'request_data' in locals() else None,
                    request_type=request_data.get('request_type',
                                                  'unknown') if 'request_data' in locals() else 'unknown'
                ))
            finally:
                self._active_workers -= 1
                if 'request_data' in locals():
                    self.request_queue.task_done()

    async def fetch_multiple(self, requests: List[Dict[str, Any]]) -> List[SprintFetchResult]:
        """Queue multiple requests and return results"""
        await self.start_workers()

        # Queue all requests
        for request in requests:
            await self.request_queue.put(request)

        # Collect results
        results = []
        for _ in range(len(requests)):
            result = await self.response_queue.get()
            results.append(result)

        return results

    async def shutdown(self):
        """Gracefully shutdown workers"""
        if not self._workers_started:
            return

        # Wait for queue to be empty
        await self.request_queue.join()

        # Send sentinel values to stop workers
        for _ in range(self.config.max_concurrent_requests):
            await self.request_queue.put(None)

        # Wait for workers to finish
        while self._active_workers > 0:
            await asyncio.sleep(0.1)


# Data Processing Classes
class SprintDataProcessor:
    @staticmethod
    def process_sprint_responses(responses: List[SprintFetchResult], max_loop_count: int) -> pd.DataFrame:
        """Process sprint API responses into DataFrame"""
        sprint_responses = [r for r in responses if r.request_type == 'sprint' and r.success and r.data]

        if not sprint_responses:
            return pd.DataFrame()

        # Process sprint data
        sprint_values = []
        for response in sprint_responses[:max_loop_count]:
            if 'values' in response.data:
                sprint_values.extend(response.data['values'])

        if not sprint_values:
            return pd.DataFrame()

        df = pd.DataFrame(sprint_values)
        if 'self' in df.columns:
            df.drop(columns=['self'], inplace=True)

        return df

    @staticmethod
    def process_velocity_data(responses: List[SprintFetchResult]) -> pd.DataFrame:
        """Process velocity statistics into DataFrame"""
        velocity_responses = [r for r in responses if r.request_type == 'velocity' and r.success and r.data]

        if not velocity_responses:
            return pd.DataFrame()

        velocity_data = velocity_responses[0].data
        if 'velocityStatEntries' not in velocity_data:
            return pd.DataFrame()

        df_velocity = pd.DataFrame.from_dict(
            velocity_data["velocityStatEntries"],
            orient="index"
        ).reset_index()
        df_velocity['index'] = df_velocity['index'].astype('int64')

        return df_velocity

    @staticmethod
    def process_burndown_data(responses: List[SprintFetchResult]) -> pd.DataFrame:
        """Process burndown chart data into DataFrame"""
        burndown_responses = [r for r in responses if r.request_type == 'burndown' and r.success and r.data]

        if not burndown_responses:
            return pd.DataFrame()

        successful_data = []
        for response in burndown_responses:
            response.data['sprint_id'] = response.sprint_id
            successful_data.append(response.data)

        df_burndown = pd.json_normalize(successful_data)

        # Process complex columns
        complex_columns = ['changes', 'issueToParentKeys', 'issueToSummary',
                           'workRateData', 'openCloseChanges']
        for col in complex_columns:
            if col in df_burndown.columns:
                df_burndown[col] = df_burndown[col].map(lambda x: x[0] if x else {})

        return df_burndown

class AsyncJiraWorkflowExtractor:
    def __init__(self, jira_entry, logger):
        self.jira_entry = jira_entry
        self.base_url = self.jira_entry.url
        self.logger = logger

    async def get_project_workflows(self, project_key: str) -> Dict[str, Any]:
        """
        Get all workflows associated with a project

        Args:
            project_key: JIRA project key (e.g., 'PROJ')

        Returns:
            Dictionary containing workflow information
        """

        timeout = aiohttp.ClientTimeout(total=30)

        async with aiohttp.ClientSession(headers=self.jira_entry.custom_properties, timeout=timeout) as session:
            try:
                # Get project information
                project_url = f"{self.base_url}/rest/api/3/project/{project_key.upper()}"
                project_data = await fetch_with_retries(
                    session, 'GET', project_url, my_logger=self.logger
                )
                if not project_data['success']:
                    self.logger.error(f"Failed to fetch project data for {project_key}")
                    return {}

                # Get workflow schemes for the project
                workflow_scheme_url = f"{self.base_url}/rest/api/3/workflowscheme/project"
                params = {'projectId': project_data['result']['id']}
                self.logger.debug(f"params: {params}")

                scheme_data = await fetch_with_retries(
                    session, 'GET', workflow_scheme_url, params=params, my_logger=self.logger
                )

                if not scheme_data['success']:
                    self.logger.error(f"Failed to fetch workflow scheme for project {project_key}")
                    self.logger.debug("Check if you have admin permissions")
                    self.logger.debug(f"response: {scheme_data}")
                    return {}
                self.logger.debug(f"workflow scheme data: {scheme_data}")
                workflows_info = {
                    'project': {
                        'key': project_key,
                        'name': project_data['name'],
                        'id': project_data['id']
                    },
                    'workflows': {}
                }

                if 'values' in scheme_data['result'] and scheme_data['result']['values']:
                    scheme_id = scheme_data['result']['values'][0]['id']

                    # Get detailed workflow scheme information
                    detailed_scheme_url = f"{self.base_url}/rest/api/3/workflowscheme/{scheme_id}"
                    detailed_scheme = await fetch_with_retries(
                        session, 'GET', detailed_scheme_url, my_logger=self.logger
                    )

                    if not detailed_scheme['success']:
                        self.logger.error(f"Failed to fetch detailed workflow scheme {scheme_id}")
                        return workflows_info

                    # Process each workflow in the scheme
                    workflow_names = set()

                    if 'issueTypeMappings' in detailed_scheme['result']:
                        for issue_type, workflow_name in detailed_scheme['result']['issueTypeMappings'].items():
                            workflow_names.add(workflow_name)

                    # Handle default workflow if exists
                    if 'defaultWorkflow' in detailed_scheme['result']:
                        workflow_names.add(detailed_scheme['result']['defaultWorkflow'])

                    # Fetch details for each unique workflow
                    for workflow_name in workflow_names:
                        workflow_details = await self.get_workflow_details(session, workflow_name)
                        workflows_info['workflows'][workflow_name] = workflow_details

                return workflows_info
            except Exception as e:
                self.logger.error(f"Error fetching project workflows: {e}")
                return {}

    async def get_workflow_details(self, session: aiohttp.ClientSession, workflow_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific workflow

        Args:
            session: aiohttp ClientSession
            workflow_name: Name of the workflow

        Returns:
            Dictionary containing workflow details with statuses and transitions
        """
        try:
            # Get workflow information
            workflow_url = f"{self.base_url}/rest/api/3/workflow/search"
            params = {'workflowName': workflow_name}
            workflow_data = await fetch_with_retries(
                session, 'GET', workflow_url, params=params, my_logger=self.logger
            )
            if not workflow_data['success']:
                self.logger.error(f"Failed to fetch workflow data for {workflow_name}")
                return {'error': f'Workflow {workflow_name} not found'}

            # if not workflow_data or not workflow_data.get('values'):
            #     return {'error': f'Workflow {workflow_name} not found'}

            workflow_info = workflow_data['result']['values'][0]

            # Get workflow transitions
            transitions_url = f"{self.base_url}/rest/api/3/workflow/transitions/{workflow_info['id']}"
            transitions_data = await fetch_with_retries(
                session, 'GET', transitions_url, my_logger=self.logger
            )

            if not transitions_data['success']:
                return {'error': f'Failed to fetch transitions for workflow {workflow_name}'}

            # Process statuses and transitions
            statuses = {}

            # Build status information
            for transition in transitions_data['result']:
                from_status = transition.get('from', [])
                to_status = transition.get('to', {})
                transition_name = transition.get('name', '')
                transition_id = transition.get('id', '')

                # Handle 'to' status
                if to_status:
                    status_name = to_status.get('name', '')
                    status_id = to_status.get('id', '')

                    if status_name not in statuses:
                        statuses[status_name] = {
                            'id': status_id,
                            'name': status_name,
                            'transitions_to': [],
                            'transitions_from': []
                        }

                # Handle 'from' statuses
                for from_stat in from_status:
                    from_status_name = from_stat.get('name', '')
                    from_status_id = from_stat.get('id', '')

                    if from_status_name not in statuses:
                        statuses[from_status_name] = {
                            'id': from_status_id,
                            'name': from_status_name,
                            'transitions_to': [],
                            'transitions_from': []
                        }

                    # Add transition information
                    transition_info = {
                        'id': transition_id,
                        'name': transition_name,
                        'to_status': to_status.get('name', ''),
                        'to_status_id': to_status.get('id', '')
                    }

                    # Avoid duplicates
                    if not any(t['id'] == transition_id for t in statuses[from_status_name]['transitions_to']):
                        statuses[from_status_name]['transitions_to'].append(transition_info)

                    # Add reverse mapping
                    reverse_transition_info = {
                        'from_status': from_status_name,
                        'from_status_id': from_status_id,
                        'transition_id': transition_id,
                        'transition_name': transition_name
                    }

                    if to_status.get('name'):
                        target_status = to_status['name']
                        if not any(t['transition_id'] == transition_id for t in
                                   statuses[target_status]['transitions_from']):
                            statuses[target_status]['transitions_from'].append(reverse_transition_info)

            # Find paths to "Done" status
            done_paths = self.find_paths_to_done(statuses)

            return {
                'workflow_name': workflow_name,
                'workflow_id': workflow_info['id'],
                'description': workflow_info.get('description', ''),
                'statuses': statuses,
                'paths_to_done': done_paths,
                'total_statuses': len(statuses)
            }

        except Exception as e:
            self.logger.error(f"Error fetching workflow details for {workflow_name}: {e}")
            return {'error': str(e)}

    def find_paths_to_done(self, statuses: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Find all possible paths from each status to 'Done' status

        Args:
            statuses: Dictionary of status information

        Returns:
            Dictionary mapping each status to possible paths to Done
        """
        done_status = None
        # Find the "Done" status (case-insensitive)
        for status_name in statuses:
            if status_name.lower() in ['done', 'closed', 'resolved']:
                done_status = status_name
                break

        if not done_status:
            return {'note': ['No Done/Closed/Resolved status found in workflow']}

        paths = {}

        for status_name in statuses:
            if status_name == done_status:
                paths[status_name] = ['Already at Done status']
            else:
                # Check direct transitions to Done
                direct_transitions = []
                for transition in statuses[status_name]['transitions_to']:
                    if transition['to_status'].lower() in ['done', 'closed', 'resolved']:
                        direct_transitions.append({
                            'transition_name': transition['name'],
                            'transition_id': transition['id'],
                            'path': f"{status_name} -> {transition['to_status']}"
                        })

                if direct_transitions:
                    paths[status_name] = direct_transitions
                else:
                    paths[status_name] = ['No direct path to Done status found']

        return paths

    def export_to_yaml(self, workflows_data: Dict[str, Any], output_file: str = 'jira_workflows.yaml'):
        """
        Export workflow data to YAML file

        Args:
            workflows_data: Workflow information dictionary
            output_file: Output YAML file path
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(workflows_data, f, default_flow_style=False, sort_keys=False, indent=2)
            self.logger.info(f"Workflow data exported to {output_file}")
            return
        except Exception as e:
            self.logger.error(f"Error exporting to YAML: {e}")

@inject
async def extract_workflows_standalone(
project_key: str,
    output_file: str = 'jira_workflows.yaml',
    jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
    my_logger: Logger = Provide[LoggerContainer.logger]
):
    extractor = AsyncJiraWorkflowExtractor(jira_entry, my_logger)
    workflows_data = await extractor.get_project_workflows(project_key)

    if not workflows_data:
        my_logger.error("No workflow data found")
        return {}

    extractor.export_to_yaml(workflows_data, output_file)

    for workflow_name, workflow_data in workflows_data['workflows'].items():
        if 'error' not in workflow_data:
            my_logger.info(f"Workflow: {workflow_name}")
            my_logger.info(f"  Total statuses: {workflow_data.get('total_statuses', 0)}")

            # Show paths to Done
            if 'paths_to_done' in workflow_data:
                my_logger.info(f"  Paths to Done status:")
                for status, paths in workflow_data['paths_to_done'].items():
                    if isinstance(paths, list) and len(paths) > 0:
                        if isinstance(paths[0], dict):
                            for path in paths:
                                my_logger.info(f"    {status}: {path.get('path', 'N/A')}")
                        else:
                            my_logger.info(f"    {status}: {paths[0]}")

    return workflows_data

# Request Builders
class JiraRequestBuilder:
    def __init__(self, jira_entry, config: SprintFetchConfig):
        self.jira_entry = jira_entry
        self.config = config

    def build_sprint_requests(self, board_id: int, max_loop_count: int) -> List[Dict[str, Any]]:
        """Build requests for sprint data"""
        requests = []
        url = f"{self.jira_entry.url}/rest/agile/1.0/board/{board_id}/sprint"

        for i in range(max_loop_count):
            requests.append({
                'method': 'GET',
                'url': url,
                'params': {
                    'maxResults': self.config.max_results_per_page,
                    'startAt': i * self.config.max_results_per_page
                },
                'request_type': 'sprint'
            })

        return requests

    def build_velocity_request(self, board_id: int) -> Dict[str, Any]:
        """Build request for velocity data"""
        url = f"{self.jira_entry.url}/rest/greenhopper/1.0/rapid/charts/velocity"
        return {
            'method': 'GET',
            'url': url,
            'params': {'rapidViewId': board_id},
            'request_type': 'velocity'
        }

    def build_burndown_requests(self, board_id: int, sprint_ids: List[int]) -> List[Dict[str, Any]]:
        """Build requests for burndown data"""
        requests = []
        url = f"{self.jira_entry.url}/rest/greenhopper/1.0/rapid/charts/scopechangeburndownchart"

        for sprint_id in sprint_ids:
            requests.append({
                'method': 'GET',
                'url': url,
                'params': {'rapidViewId': board_id, 'sprintId': sprint_id},
                'sprint_id': sprint_id,
                'request_type': 'burndown'
            })

        return requests


# Database Operations (same as before)
class SprintDatabaseManager:
    @staticmethod
    async def upsert_sprints(session, df: pd.DataFrame) -> str:
        """Upsert sprint data to database"""
        if df.empty:
            return "+0"

        # Cast date columns
        df = SprintDatabaseManager._cast_columns(
            df, ['startTime', 'endTime', 'completeTime', 'now'], pd.Int64Dtype()
        )
        df = SprintDatabaseManager._cast_columns(
            df, ['startDate', 'endDate', 'completeDate', 'createdDate'], "datetime"
        )

        # Remove unnecessary columns
        columns_to_drop = ['index', 'sprint_id']
        df.drop(columns=[col for col in columns_to_drop if col in df.columns], inplace=True)

        result = await upsert_async(session, Sprint, df, on_conflict_update=True)
        return f"+{df.shape[0]}"

    @staticmethod
    def _cast_columns(df: pd.DataFrame, columns: List[str], dtype) -> pd.DataFrame:
        """Helper method to cast columns to specified dtype"""
        for col in columns:
            if col in df.columns:
                if dtype == 'datetime':
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                else:
                    df[col] = df[col].astype(dtype)
        return df


# Main Service Class
class SprintDetailsService:
    def __init__(self, config: SprintFetchConfig = None):
        self.config = config or SprintFetchConfig()
        self.data_processor = SprintDataProcessor()
        self.db_manager = SprintDatabaseManager()

    async def get_board_ids(self, session, project_key: str) -> List[int]:
        """Get scrum board IDs for the project"""
        stmt = select(AllBoards.id).filter(
            (AllBoards.projectKey.in_([project_key.upper()])) &
            (AllBoards.type.in_(['scrum']))
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def fetch_sprint_data_for_board(self, http_client: QueuedHttpClient,
                                          request_builder: JiraRequestBuilder,
                                          board_id: int, max_loop_count: int,
                                          my_logger) -> pd.DataFrame:
        """Fetch and process all sprint data for a single board"""

        # Build initial requests (sprint data + velocity)
        sprint_requests = request_builder.build_sprint_requests(board_id, max_loop_count)
        velocity_request = request_builder.build_velocity_request(board_id)
        initial_requests = sprint_requests + [velocity_request]

        # Fetch sprint and velocity data
        my_logger.debug(f"Fetching {len(initial_requests)} initial requests for board {board_id}")
        responses = await http_client.fetch_multiple(initial_requests)

        # Log any failures
        failed_responses = [r for r in responses if not r.success]
        if failed_responses:
            my_logger.warning(f"Failed {len(failed_responses)} initial requests for board {board_id}")

        # Process basic sprint data
        df_sprints = self.data_processor.process_sprint_responses(responses, max_loop_count)
        if df_sprints.empty:
            my_logger.warning(f"No sprint data found for board {board_id}")
            return pd.DataFrame()

        # Process velocity data
        df_velocity = self.data_processor.process_velocity_data(responses)
        if not df_velocity.empty:
            df_sprints = df_sprints.merge(df_velocity, how='left', left_on='id', right_on='index')
            my_logger.debug(f"Merged velocity data for {len(df_velocity)} sprints")

        # Fetch burndown data for each sprint
        sprint_ids = df_sprints['id'].tolist()
        if sprint_ids:
            my_logger.debug(f"Fetching burndown data for {len(sprint_ids)} sprints")
            burndown_requests = request_builder.build_burndown_requests(board_id, sprint_ids)
            burndown_responses = await http_client.fetch_multiple(burndown_requests)

            # Log burndown failures
            failed_burndown = [r for r in burndown_responses if not r.success]
            if failed_burndown:
                my_logger.warning(f"Failed {len(failed_burndown)} burndown requests for board {board_id}")

            # Process burndown data
            df_burndown = self.data_processor.process_burndown_data(burndown_responses)
            if not df_burndown.empty:
                df_sprints = df_sprints.merge(df_burndown, how='left', left_on='id', right_on='sprint_id')
                my_logger.debug(f"Merged burndown data for {len(df_burndown)} sprints")

        return df_sprints


@inject
async def get_sprint_details(
        project_key: str, max_loop_count: int = 3,
        config: SprintFetchConfig = None,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        my_logger: Logger = Provide[LoggerContainer.logger],

):
    """
    Fetch sprint details for a project using queued requests with your existing retry logic.

    Args:
        project_key: The project key to fetch sprints for
        max_loop_count: Maximum number of pages to fetch (defaults to 3)
        config: Configuration for concurrency and retries

    Returns:
        String indicating success (+count) or failure (FAILED)
    """
    asyncio.current_task().set_name("get_sprint_details")
    if config is None:
        config = SprintFetchConfig()

    service = SprintDetailsService(config)
    try:
        app_container.schema.override(project_key)

        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            # Get board IDs
            board_ids = await service.get_board_ids(pg_async_session, project_key)

            if not board_ids:
                my_logger.warning(f"No scrum boards found for project {project_key}")
                return "+0"

            my_logger.debug(f"Found {len(board_ids)} boards for project {project_key}")
            total_sprints = 0
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(
                    headers=jira_entry.custom_properties, timeout=timeout
            ) as http_session:
                http_client = QueuedHttpClient(http_session, config)
                request_builder = JiraRequestBuilder(jira_entry, config)

                try:
                    for board_id in board_ids:
                        my_logger.debug(f"Processing board {board_id}")

                        # Fetch and process data for this board
                        df_final = await service.fetch_sprint_data_for_board(
                            http_client, request_builder, board_id, max_loop_count, my_logger
                        )

                        if not df_final.empty:
                            # Debug output
                            with DataframeDebugger() as debugger:
                                debugger.debug_dataframe(df_final, f"board_{board_id}_final.xlsx")

                            # Upsert to database
                            result = await service.db_manager.upsert_sprints(pg_async_session, df_final)
                            my_logger.debug(f"Board {board_id} result: {result}")

                            if result.startswith('+'):
                                total_sprints += int(result[1:])
                        else:
                            my_logger.warning(f"No data processed for board {board_id}")

                finally:
                    await http_client.shutdown()

            await pg_async_session.commit()
            my_logger.debug(f"get_sprint_details completed successfully! Total sprints: {total_sprints}")
            return f"+{total_sprints}"
    except Exception as e:
        my_logger.error(f"Error in get_sprint_details: {str(e)}")
        handle_exception(e)
        return "FAILED"
    # ret_string = ""
    # try:
    #     app_container.schema.override(project_key)
    #     async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
    #         # with Database(schema=project_key).session() as pg_session:
    #         stmt = select(AllBoards.id).filter(
    #             (AllBoards.projectKey.in_([project_key.upper()])) & (AllBoards.type.in_(['scrum']))
    #         )
    #         result = await pg_async_session.execute(stmt)
    #         rows: list = result.scalars().all()
    #
    #         async with aiohttp.ClientSession(headers=jira_entry.custom_properties) as http_session:
    #             for board_id in rows:
    #                 url = f"{jira_entry.url}/rest/agile/1.0/board/{board_id}/sprint"
    #
    #                 tasks = []
    #                 for i in range(max_loop_count):
    #                     payload_dict = {'maxResults': 50, 'startAt': i * 50}
    #                     tasks.append(fetch_with_retries_get_new(http_session, url, payload_dict))
    #
    #                 url = f"{jira_entry.url}/rest/greenhopper/1.0/rapid/charts/velocity"
    #                 payload_dict = {'rapidViewId': board_id}
    #                 tasks.append(
    #                     fetch_with_retries_get_new(http_session, url, payload_dict)
    #                 )
    #
    #                 responses = await asyncio.gather(*tasks, return_exceptions=True)
    #                 my_logger.debug("responses are:")
    #                 my_logger.debug(f"{responses}")
    #
    #                 successful_responses = [resp["result"] for resp in responses if resp.get("success")]
    #                 exceptions = [resp["exception"] for resp in responses if not resp.get("success")]
    #
    #                 # my_logger.debug(
    #                 # f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}"
    #                 # )
    #                 # my_logger.debug(f"# of exceptions: {len(exceptions)}")
    #
    #                 # for result in responses:
    #                 #     if isinstance(result, Exception):
    #                 #         my_logger.error(f"Task raised an exception: {result}")
    #                 my_logger.debug(f"{successful_responses}")
    #
    #                 df = pd.DataFrame(
    #                     # [value for response in responses[:max_loop_count] for value in response["values"]]
    #                     [value for response in successful_responses[:max_loop_count] for value in response["values"]]
    #                 )
    #                 if 'self' in df.columns:
    #                     df.drop(columns=['self'], inplace=True)
    #
    #                 # velocity_stat_entries = responses[max_loop_count:][0]["velocityStatEntries"]
    #                 velocity_stat_entries = successful_responses[max_loop_count:][0]["velocityStatEntries"]
    #
    #                 df_velocity = pd.DataFrame.from_dict(velocity_stat_entries, orient="index").reset_index()
    #                 df_velocity['index'] = df_velocity['index'].astype('int64')
    #
    #                 df = df.merge(df_velocity, how='left', left_on='id', right_on='index')
    #                 with DataFrameDebugger() as debugger:
    #                     debug_dataframe = debugger.debug_dataframe
    #                     debug_dataframe(df, f"df.xlsx")
    #                     debug_dataframe(df_velocity, f"df_velocity.xlsx")
    #
    #                 tasks = []
    #                 semaphore = asyncio.Semaphore(15)
    #                 url = f"{jira_entry.url}/rest/greenhopper/1.0/rapid/charts/scopechangeburndownchart"
    #                 for sprint_id in df['id'].tolist():
    #                     payload_dict = {'rapidViewId': board_id, 'sprintId': sprint_id}
    #                     tasks.append(
    #                         fetch_sprints(
    #                             sprint_id, http_session, url,
    #                             payload=payload_dict, semaphore=semaphore
    #                         )
    #                     )
    #                 responses = await asyncio.gather(*tasks, return_exceptions=True)
    #
    #                 responses = [response for response in responses]
    #                 df_burndown = pd.json_normalize(responses)
    #
    #                 for col in ['changes', 'issueToParentKeys', 'issueToSummary', 'workRateData',
    #                             'openCloseChanges']:
    #                     # df_burndown[col] = df_burndown[col].apply(list_of_dicts_to_dict)
    #                     df_burndown[col] = df_burndown[col].map(lambda x: x[0] if x else {})
    #
    #                 df = df.merge(df_burndown, how='left', left_on='id', right_on='sprint_id')
    #                 df.drop(columns=['index', 'sprint_id'], inplace=True)
    #
    #                 # for col in ['startTime', 'endTime', 'completeTime', 'now']:
    #                 #     df[col] = df[col].astype(pd.Int64Dtype())
    #
    #                 df = cast_columns(df, ['startTime', 'endTime', 'completeTime', 'now'], pd.Int64Dtype())
    #                 df = cast_columns(df, ['startDate', 'endDate', 'completeDate', 'createdDate'], "datetime")
    #
    #                 # for col in ['startDate', 'endDate', 'completeDate', 'createdDate']:
    #                 #     # Safely convert to datetime
    #                 #     df[col] = pd.to_datetime(df[col], errors="coerce")
    #                 with DataFrameDebugger() as debugger:
    #                     debug_dataframe = debugger.debug_dataframe
    #                     debug_dataframe(df_burndown, "df_burndown.xlsx")
    #                     debug_dataframe(df, "df_final.xlsx")
    #
    #                 x = await upsert_async(
    #                     pg_async_session, Sprint, df, on_conflict_update=True
    #                 )
    #                 my_logger.debug(f"return value = {x}")
    #                 ret_string = f"+{df.shape[0]}"
    #                 await pg_async_session.commit()
    #     my_logger.debug("get_sprint_details completed!!!")
    # except Exception as e:
    #     ret_string = "FAILED"
    #     handle_exception(e)
    # finally:
    #     return ret_string


class IssueTypeClassification(Enum):
    initiative = 1
    epic = 2
    standard = 3
    subtask = 4


def prepare_issue_classification_data(pg_session) -> pd.DataFrame:
    topq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                            Issue.issue_hierarchy_level,
                            literal(1).label('level'),
                            case(

                                (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                (Issue.issuetype == 'Epic', 'epic'),
                                (Issue.isSubTask.is_(True), 'subtask'),

                                else_="standard"
                            ).label("issueclass"),
                            cast(cast(Issue.id, TEXT), LtreeType).label("path_id"),
                            cast(func.replace(Issue.key, "-", "_"), LtreeType).label("path_key")
                            )
    topq = topq.filter(Issue.parent_key.is_(None))
    topq = topq.cte('cte', recursive=True)

    bottomq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                               Issue.issue_hierarchy_level,
                               topq.c.level + 1,
                               case(
                                   (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                   (Issue.issuetype == 'Epic', 'epic'),
                                   (Issue.isSubTask.is_(True), 'subtask'),
                                   else_="standard"
                               ).label("issueclass"),
                               topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                               topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                               )
    bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
    recursive_q = topq.union_all(bottomq)
    res = pg_session.query(recursive_q).all()
    df = pd.DataFrame(res)
    # df = pd.read_sql(session.query(recursive_q).statement, session.bind)
    return df


def chunked_iterable(iterable, size):
    """Yield chunks of `size` from `iterable`."""
    it = iter(iterable)
    for first in it:
        yield [first] + list(islice(it, size - 1))


@inject
async def upsert_issue_classification(
        project_key: str,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    total_rows = 0

    try:
        app_container.schema.override(project_key)
        with app_container.database_rw().update_schema(project_key).session() as pg_session:
            df = prepare_issue_classification_data(pg_session)
            my_logger.debug(f"Total rows: {df.shape[0]}")
        # Copy id, key value to respective columns
        if df.empty:
            return

        my_logger.debug(f"processing df")
        for issue_type in IssueTypeClassification:
            df[f'{issue_type.name}_id'] = df[df['issueclass'] == issue_type.name]['id']
            df[f'{issue_type.name}_key'] = df[df['issueclass'] == issue_type.name]['key']

        dict_initiative = df[df['issueclass'] == "initiative"][
            ['key', 'initiative_id', "initiative_key"]].set_index('key').T.to_dict('list')

        for count, field in enumerate(["initiative_id", "initiative_key"]):
            df.loc[df['issueclass'] == "epic", [field]] = \
                df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])

        dict_epic = df[df['issueclass'] == "epic"][
            ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key"]].set_index('key').T.to_dict('list')

        for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key", ]):
            df.loc[df['issueclass'] == "standard", [field]] = \
                df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])

        dict_standard = df[df['issueclass'] == "standard"][
            ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key", "standard_id",
             "standard_key"]].set_index(
            'key').T.to_dict('list')

        for count, field in enumerate(
                ["initiative_id", "initiative_key", "epic_id", "epic_key", "standard_id", "standard_key"]
        ):
            df.loc[df['issueclass'] == "subtask", [field]] = \
                df['parent_key'].map(
                    lambda x: np.nan if dict_standard.get(x, [None] * 6)[count] is None else
                    dict_standard.get(x, [None] * 6)[count]
                )

        # Subtasks mapped to epic
        for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key"]):
            df.loc[(df['issueclass'] == "subtask") & (pd.isna(df["standard_id"])), [field]] = \
                df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])

        # Subtasks mapped to initiative
        for count, field in enumerate(["initiative_id", "initiative_key"]):
            df.loc[(df['issueclass'] == "subtask") & (pd.isna(df["epic_id"])), [field]] = \
                df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])

        df.drop(columns=['isSubTask', 'parent_key', 'level', 'issuetype'], inplace=True)
        my_logger.debug(f"setting int types")
        # for col in ['initiative_id', 'epic_id', 'standard_id', 'subtask_id']:
        #     df[col] = df[col].astype(pd.Int64Dtype())
        df = cast_columns(df, ['initiative_id', 'epic_id', 'standard_id', 'subtask_id'], pd.Int64Dtype())
        # df[['initiative_id', 'epic_id', 'standard_id', 'subtask_id']] = df[
        #     ['initiative_id', 'epic_id', 'standard_id', 'subtask_id']].astype(pd.Int64Dtype())

        # df = df.astype({'initiative_id': int, 'epic_id': int, 'standard_id': int, 'subtask_id': int})

        # my_logger.debug(f"Memory used: {humansize(sys.getsizeof(df))}")
        # my_logger.debug(f"Memory used: {humansize(df.memory_usage(index=False, deep=True).sum())}")
        # my_logger.debug(f"df information: {df.info()}")
        total_rows = df.shape[0]

        # Initialize the rich progress bar

        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            asyncio.current_task().set_name("upsert_issue_classification_db")
            conflict_condition = and_(
                or_(
                    and_(
                        getattr(IssueClassification, "initiative_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "initiative_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "initiative_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "initiative_id").is_(None),
                    ),
                    getattr(IssueClassification, "initiative_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "initiative_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "epic_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "epic_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "epic_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "epic_id").is_(None),
                    ),
                    getattr(IssueClassification, "epic_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "epic_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "standard_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "standard_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "standard_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "standard_id").is_(None),
                    ),
                    getattr(IssueClassification, "standard_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "standard_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "subtask_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "subtask_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "subtask_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "subtask_id").is_(None),
                    ),
                    getattr(IssueClassification, "subtask_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "subtask_id"),
                ),
            )
            my_logger.info(f"conflict_condition type = {type(conflict_condition)}")
            my_logger.debug(f"{conflict_condition}")

            await upsert_async(pg_async_session, IssueClassification, df, conflict_condition=conflict_condition)
            # for i, start in enumerate(range(0, total_rows, batch_size)):
            #     batch_df = df.iloc[start:start + batch_size]
            #     await upsert_async(pg_async_session, IssueClassification, batch_df, )
            #     my_logger.debug(f"batch {i} completed")
            await pg_async_session.commit()

        # for batch_data in chunked_iterable(df.to_dict('records'), batch_size):
        #     # Convert the list of dictionaries back to a DataFrame
        #     batch_df = pd.DataFrame(batch_data)
        #
        #     upsert(pg_session, IssueClassification, batch_df, "id")
    except Exception as e:
        handle_exception(e)

    finally:
        return f"+{total_rows}"


@inject
def create_refresh_mv(
        project_list: list,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    view_name = "run_details_view"
    my_logger.debug(f"executing create_refresh_mv. project list; {project_list}")
    project_names = ["acq", "plat"]

    app_container.schema.override('public')
    with app_container.database_rw().update_schema('public').session() as pg_session:
        # Construct the union query based on the list of project names
        union_queries = [
            f"SELECT '{project}' AS schema_name, topic, last_run FROM {project}.run_details_jira"
            for project in project_names
        ]
        union_query = " UNION ".join(union_queries)

        query = text(f"""
                CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} AS
                {union_query}
                ORDER BY schema_name
                """)
        pg_session.execute(query)
        pg_session.commit()

        pg_session.execute(text(f"REFRESH MATERIALIZED VIEW {view_name}"))
        pg_session.commit()
    my_logger.debug("create_refresh_mv completed!!!")


def format_time_difference(total_time_ns: int):
    # Convert nanoseconds to timedelta
    diff_s = total_time_ns / 1_000_000_000
    time_diff = timedelta(seconds=diff_s)

    # Extract hours, minutes, seconds, and milliseconds
    total_seconds = int(time_diff.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int(time_diff.microseconds / 1000)

    # Format the result as hh:mm:ss.ms
    return f"{hours:02}:{minutes:02}:{seconds:02}.{milliseconds:03}"


# Rate limit constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 5000  # in milliseconds
MAX_RETRY_DELAY = 10000  # in milliseconds
JITTER_MULTIPLIER_RANGE = (0.5, 1.5)

class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit breaker tripped, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 3
    recovery_timeout: float = 30.0  # seconds
    half_open_max_calls: int = 3


class GlobalCircuitBreaker:
    """
    Global circuit breaker that coordinates rate limiting across multiple async processes.
    Thread-safe and works on Windows/Unix.
    """
    @inject
    def __init__(
            self,  config: CircuitBreakerConfig = None,

    ):
        self.config = config or CircuitBreakerConfig()
        # self.logger = logger
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0
        self._half_open_calls = 0

        # Asyncio events for coordination
        self._circuit_open_event = asyncio.Event()
        self._rate_limit_event = asyncio.Event()

        # Lock for thread safety
        self._lock = asyncio.Lock()

        # Rate limiting coordination
        self._active_requests = 0
        self._backoff_until = 0


    @inject
    async def can_execute(self, logger: Logger = Provide[LoggerContainer.logger]) -> bool:
        """Check if request can be executed based on circuit state."""
        asyncio.current_task().set_name("can_execute")
        async with self._lock:
            current_time = time.time()

            if self._state == CircuitState.OPEN:
                if current_time - self._last_failure_time > self.config.recovery_timeout:
                    self._state = CircuitState.HALF_OPEN
                    self._half_open_calls = 0
                    self._circuit_open_event.clear()
                    logger.debug("can_execute: Circuit breaker transitioning to HALF_OPEN state.")
                    return True
                return False

            elif self._state == CircuitState.HALF_OPEN:
                if self._half_open_calls >= self.config.half_open_max_calls:
                    return False
                return True

            # CLOSED state - check for active rate limiting
            if current_time < self._backoff_until:
                logger.debug(f"can_execute: Rate limited. Waiting until {self._backoff_until}.")
                return False

            return True

    @inject
    async def record_success(self, logger: Logger = Provide[LoggerContainer.logger]):
        """Record successful request."""
        asyncio.current_task().set_name("record_success")
        async with self._lock:
            if self._state == CircuitState.HALF_OPEN:
                self._failure_count = 0
                self._state = CircuitState.CLOSED
                self._circuit_open_event.clear()
                logger.debug(f"record_success: state= {self._state}")
            elif self._state == CircuitState.CLOSED:
                self._failure_count = max(0, self._failure_count - 1)
                logger.debug(f"record_success: _failure_count = {self._failure_count}")
    @inject
    async def record_rate_limit(self, retry_delay: float, logger: Logger = Provide[LoggerContainer.logger]):
        """Record rate limit hit and coordinate backoff."""
        asyncio.current_task().set_name("record_rate_limit")
        async with self._lock:
            current_time = time.time()
            self._backoff_until = current_time + (retry_delay / 1000)
            self._failure_count += 1

            if self._failure_count >= self.config.failure_threshold:
                self._state = CircuitState.OPEN
                self._last_failure_time = current_time
                self._circuit_open_event.set()

            # Set rate limit event to signal other processes
            self._rate_limit_event.set()
            logger.debug(f"record_rate_limit: Rate limit hit. Backing off until {datetime.fromtimestamp(self._backoff_until,  tz=ZoneInfo("Asia/Kolkata")).strftime("%Y-%m-%d %H:%M:%S %z")}.")

    @inject
    async def wait_for_recovery(self, timeout: Optional[float] = None, logger: Logger = Provide[LoggerContainer.logger]):
        """Wait for circuit breaker to recover or rate limit to clear."""
        asyncio.current_task().set_name("wait_for_recovery")
        if self._state == CircuitState.OPEN:
            try:
                await asyncio.wait_for(
                    self._circuit_open_event.wait(),
                    timeout=timeout
                )
                logger.debug("wait_for_recovery: Circuit breaker transitioned to CLOSED state.")
            except asyncio.TimeoutError:
                logger.debug("wait_for_recovery: Timeout waiting for circuit breaker recovery.")
                pass

        # Also wait for any active rate limiting to clear
        current_time = time.time()
        if current_time < self._backoff_until:
            wait_time = self._backoff_until - current_time
            if timeout is None or wait_time <= timeout:
                logger.debug(f"wait_for_recovery: Waiting for rate limit to clear. Sleeping for {wait_time:.2f} seconds.")
                await asyncio.sleep(wait_time)
        else:
            logger.debug("wait_for_recovery: Rate limit cleared.")
    @inject
    async def enter_request(self, logger: Logger = Provide[LoggerContainer.logger]):
        """Enter a request context (for half-open state tracking)."""
        asyncio.current_task().set_name("enter_request")
        async with self._lock:
            self._active_requests += 1
            if self._state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
            logger.debug(f"enter_request: Entered request context. Active requests: {self._active_requests}")
    @inject
    async def exit_request(self, logger: Logger = Provide[LoggerContainer.logger]):
        """Exit a request context."""
        asyncio.current_task().set_name("exit_request")
        async with self._lock:
            self._active_requests = max(0, self._active_requests - 1)
            logger.debug(f"exit_request: Exited request context. Active requests: {self._active_requests}")

    @property
    def state(self) -> CircuitState:
        return self._state

    @property
    def is_open(self) -> bool:
        return self._state == CircuitState.OPEN



@inject
@debug_async_function("fetch_with_retries")
async def fetch_with_retries(
        session: aiohttp.ClientSession,
        method: str,
        url: str,
        *,
        params: Union[Dict[str, Any], None] = None,
        json_payload: Union[Dict[str, Any], None] = None,
        retries: int = MAX_RETRIES,
        my_logger: Logger = Provide[LoggerContainer.logger],
) -> Any:
    """
    Perform an HTTP request with retry logic.

    :param session: aiohttp ClientSession.
    :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').
    :param url: API endpoint URL.
    :param params: HTTP request parameters for GET.
    :param json_payload: JSON body for POST or PUT.
    :param retries: Maximum number of retries.
    :param my_logger: Logger instance.
    :return: JSON response data or None.
    """
    asyncio.current_task().set_name("fetch_with_retries")
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY
    global commit_transaction

    while retry_count <= retries:
        # Check circuit breaker before making request
        if not await global_circuit_breaker.can_execute():
            my_logger.warning("Circuit breaker is OPEN or rate limited. Waiting for recovery...")
            await global_circuit_breaker.wait_for_recovery(timeout=30.0)

            # Check again after waiting
            if not await global_circuit_breaker.can_execute():
                my_logger.error("Circuit breaker still OPEN after waiting. Aborting request.")
                return {"success": False, "exception": "Circuit breaker OPEN"}

        await global_circuit_breaker.enter_request()

        try:
            async with debug_http_request(session, method, url):
                async with session.request(
                        method=method,
                        url=url,
                        params=params,
                        json=json_payload,
                ) as response:
                    if response.status in (200, 201, 204):
                        # Success handling
                        await global_circuit_breaker.record_success()
                        if response.status == 204:
                            my_logger.info("Request successful, but no content to return.")
                            return {"success": True, "result": None}
                        else:
                            if response.headers.get("X-RateLimit-NearLimit") == "true":
                                wait_time = 2 ** retry_count
                                my_logger.warning(f"Warning: Less than 20% of the rate limit budget remains. Sleeping for {wait_time} sec")
                                # Record as a "soft" rate limit to coordinate with other processes
                                await global_circuit_breaker.record_rate_limit(wait_time * 1000)
                                await asyncio.sleep(wait_time)
                            return {"success": True, "result": await response.json()}
                    elif response.status in (429, 503) or "Retry-After" in response.headers:
                        # Rate limit hit - record in circuit breaker
                        # Retry logic - record rate limit event

                        retry_delay = await calculate_retry_delay(response, retry_delay)
                        rate_limit_tracker.record_rate_limit(url, retry_delay)
                        await global_circuit_breaker.record_rate_limit(retry_delay)

                        my_logger.warning(
                            f"Rate limited (status: {response.status}). "
                            f"Circuit breaker coordinating backoff for {retry_delay / 1000:.2f} seconds."
                        )

                        # Wait for the circuit breaker to coordinate the delay
                        await global_circuit_breaker.wait_for_recovery()
                        # my_logger.warning(
                        #     f"Rate limited or server unavailable. Retrying in {retry_delay / 1000:.2f} seconds. "
                        #     f"Consecutive rate limits: {rate_limit_tracker.consecutive_rate_limits}"
                        # )
                    else:
                        # Log error and exit
                        my_logger.error(f"Request failed with status {response.status}. url = {response.url}")

                        async with lock:
                            commit_transaction = False
                        return {"success": False, "exception": f"HTTP {response.status}"}
                    response.raise_for_status()

        except aiohttp.ClientResponseError as e:
            my_logger.info(f"HTTP error {e.status}: {e.message}")
            if e.status in (429, 503):
                await global_circuit_breaker.record_rate_limit(retry_delay)
                await global_circuit_breaker.wait_for_recovery()
            else:
                raise e
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            my_logger.error(f"Request error: {e}. Retrying {retry_count}/{retries} times.")
        except Exception as e:
            my_logger.critical(f"Unexpected error: {e}")
            raise e
        finally:
            await global_circuit_breaker.exit_request()

            # Increment retry count and add jitter
            retry_count += 1
            if retry_count > retries:
                my_logger.error(f"Exceeded maximum retries ({retries}).")
                break

            # Only add jitter if we're not coordinating through circuit breaker
            if global_circuit_breaker.state == CircuitState.CLOSED:
                jitter = random.uniform(0.8, 1.2)  # JITTER_MULTIPLIER_RANGE
                my_logger.warning(f"Adding jitter of {jitter:.2f}. Sleeping for {math.ceil(retry_delay * jitter / 1000)} seconds.")
                await asyncio.sleep(math.ceil(retry_delay * jitter / 1000))
    return None

# Example usage for monitoring circuit breaker state
async def monitor_circuit_breaker(logger: Logger):
    """Optional: Monitor circuit breaker state for debugging."""
    while True:
        state = global_circuit_breaker.state
        logger.info(f"Circuit breaker state: {state.value}")
        await asyncio.sleep(10)  # Log every 10 seconds

async def fetch_with_retries_post(
        session: aiohttp.ClientSession,
        url: str,
        json_payload: Dict[str, Any]
) -> Any:
    asyncio.current_task().set_name("fetch_with_retries_post")
    return await fetch_with_retries(
        session=session,
        method="POST",
        url=url,
        json_payload=json_payload,
    )


async def fetch_with_retries_get_new(
        session: aiohttp.ClientSession,
        url: str,
        params: Union[Dict[str, Any], None] = None,
        my_logger: Logger = Provide[LoggerContainer.logger],
) -> Any:
    asyncio.current_task().set_name("fetch_with_retries_get")
    return await fetch_with_retries(
        session=session,
        method="GET",
        url=url,
        params=params,
        my_logger=my_logger,
    )


@inject
async def fetch_with_retries_get_old(
        session: aiohttp.ClientSession, url: str,
        # auth: BasicAuth,
        # headers: dict,
        params: Union[Dict[str, Any], None] = None,
        my_logger: Logger = Provide[LoggerContainer.logger],

) -> Any:
    """
    Fetch data from the specified URL with retry logic for rate limiting.
    :param session: aiohttp ClientSession.
    :param url: API endpoint URL.
    :param params: HTTP request parameters.
    :param my_logger: Logger file instance
    :return: JSON response data.

    """
    global commit_transaction
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    while retry_count <= MAX_RETRIES:
        try:
            async with session.get(url, params=params or None) as response:
                if response.status == 200:
                    # Check for the X-RateLimit-NearLimit header
                    # my_logger.debug(f"url: {response.url}")
                    if response.headers.get("X-RateLimit-NearLimit") == "true":
                        my_logger.warning("Warning: Less than 20% of the rate limit budget remains.")
                    return await response.json()
                elif response.status == 201:
                    my_logger.info("Resource created successfully.")
                    return await response.json()
                elif response.status == 204:
                    my_logger.info("Request successful, but no content to return.")
                    my_logger.debug(f"url: {response.url}")
                    return None
                elif response.status == 429 or "Retry-After" in response.headers:
                    # Handle 429 rate limit errors and Retry-After header
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                else:
                    my_logger.error(f"Request failed with status {response.status}.")
                    my_logger.debug(await response.text())  # For additional debugging
                    async with lock:
                        commit_transaction = False
                    return None

                # Raise an exception for other HTTP errors
                response.raise_for_status()

        except ClientResponseError as e:
            my_logger.error(f"Request failed with status {e.status}.")
            if e.status != 429:
                # Only retry on 429 status
                async with lock:
                    commit_transaction = False
                raise e
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            my_logger.error(f"Request error: {e}. Retrying {retry_count}/{MAX_RETRIES} times.")
            async with lock:
                commit_transaction = False
        except Exception as e:
            my_logger.error(f"An unexpected error occurred: {e}")
            async with lock:
                commit_transaction = False
            handle_exception(e)

        if retry_delay > 0:
            retry_count += 1
            my_logger.debug(f"Retrying in {retry_delay / 1000:.2f} seconds...retry_count = {retry_count}")
            await asyncio.sleep(retry_delay / 1000)  # Convert to seconds

    raise Exception(f"Failed to fetch data after {MAX_RETRIES} retries.")


async def calculate_retry_delay(
        response: aiohttp.ClientResponse, last_retry_delay: int,
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> int:
    """
    Calculate the delay for the next retry attempt.

    :param response: The HTTP response object.
    :param last_retry_delay: The last retry delay in milliseconds.
    :param my_logger: logger instance Injected from container
    :return: The new retry delay in milliseconds.
    """
    retry_delay = -1

    # Check for the Retry-After header
    if "Retry-After" in response.headers:
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Check for the X-RateLimit-Reset header
    elif "X-RateLimit-Reset" in response.headers:
        reset_time = parser.parse(response.headers["X-RateLimit-Reset"])
        current_time = datetime.now(timezone.utc)
        wait_time = (reset_time - current_time).total_seconds() * 1000  # Convert to milliseconds
        retry_delay = max(wait_time, last_retry_delay)

    # If no headers are present but 429 status is received, double the last delay
    elif response.status == 429:
        retry_delay = min(2 * last_retry_delay, MAX_RETRY_DELAY)

    elif response.status in (503, 500) and "Retry-After" in response.headers:
        # Handle transient 5XX errors with Retry-After header
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Apply jitter
    if retry_delay > 0:
        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        retry_delay += int(retry_delay * jitter)

    # Log and handle X-RateLimit-NearLimit
    if response.headers.get("X-RateLimit-NearLimit") == "true":
        retry_delay = max(retry_delay, 5000)  # Example delay of 5 seconds
        my_logger.warning(f"Less than 20% of the budget remains. sleeping for {retry_delay / 1000} seconds.")
        # Introduce a delay when near limit is reached
        await asyncio.sleep(retry_delay / 1000)

    return retry_delay


@inject
async def fetch_changelog(
        session: aiohttp.ClientSession, issue_key: str,
        params: dict,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
) -> Any:
    changelog_semaphore = asyncio.Semaphore(20)
    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/changelog"

    async with changelog_semaphore:  # Apply semaphore limit here
        return await fetch_with_retries_get_old(session, url, params)


@inject
async def fetch_worklog(
        session: aiohttp.ClientSession, issue_key: str,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        my_logger: Logger = Provide[LoggerContainer.logger],

) -> Any:
    worklog_semaphore = asyncio.Semaphore(20)
    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/worklog"

    params = {
        "startAt": 20,
        "maxResults": 5000
    }
    all_worklogs = []

    async with worklog_semaphore:  # Apply semaphore limit here
        while True:
            response = await fetch_with_retries_get_new(session, url, params)

            if not response.get("success"):
                # Log the error and break the loop
                my_logger.error(
                    f"Failed to fetch worklogs for issue {issue_key}: {response.get('exception')}")
                return {"success": False, "exception": response.get("exception")}

            # Process successful response
            result = response.get("result", {})
            worklogs = result.get("worklogs", [])
            all_worklogs.extend(worklogs)

            # if not response or "worklogs" not in response:
            #     break
            #
            # worklogs = response.get("worklogs", [])
            # all_worklogs.extend(worklogs)
            if len(worklogs) < params["maxResults"]:
                break  # Exit loop if fewer records than maxResults

            params["startAt"] += params["maxResults"]
    # Return all worklogs if successful
    # Do this as next step
    return {"success": True, "result": all_worklogs}
    # return all_worklogs


async def fetch_comments(
        session: aiohttp.ClientSession, issue_key: str,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
) -> list:
    comments_semaphore = asyncio.Semaphore(10)

    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/comment"
    params = {
        "startAt": 100,
        "maxResults": 5000
    }
    all_comments = []

    async with comments_semaphore:  # Apply semaphore limit here
        while True:
            response = await fetch_with_retries_get_new(session, url, params)
            comments = response["result"].get("comments", [])
            all_comments.extend(comments)
            if len(comments) < params['maxResults']:
                break
            params["startAt"] += params["maxResults"]

        return all_comments



async def fetch_sprints(
        sprint_id,
        session: aiohttp.ClientSession, url, payload: dict,
        semaphore: asyncio.locks.Semaphore
):
    async with semaphore:
        # response = await fetch_with_retries_get_old(
        #     session, url, payload
        # )
        responses = await fetch_with_retries_get_new(
            session, url, payload
        )
        if responses.get("success"):
            response = responses.get("result")

            response['sprint_id'] = sprint_id
            response.pop('statisticField', None)
            if 'lastUserWhoClosedHtml' in response:
                html_content = response['lastUserWhoClosedHtml']
                soup = BeautifulSoup(html_content, 'html.parser')
                a_tag = soup.find('a')
                response['lastUserWhoClosedHtml'] = a_tag.get_text()
            for key, value in response["openCloseChanges"].items():
                for item in value:
                    html_content = item["userDisplayNameHtml"]
                    soup = BeautifulSoup(html_content, 'html.parser')
                    item["userDisplayNameHtml"] = soup.find('a').get_text()

            for key, value in response.items():
                if isinstance(value, dict):
                    response[key] = [value]
            return response
        return None


# Function to add new key-value pairs with parameters from DataFrame
@inject
def add_issue_key_issue_id(
        d, issue_key, issue_id,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    try:
        d['issue_key'] = issue_key
        d['issue_id'] = issue_id
        return d
    except Exception as e:
        my_logger.debug(
            f"these are the values: {issue_id} type: {type(issue_id)} -> {issue_key} type {type(issue_key)}")
        my_logger.debug(f"dict passed is : {d} type: {type(d)}")
        raise e


@inject
async def consume_changelog(
        queue_id: int,
        name: str,
        queue_changelog: Queue,
        project_key: str,
        # pg_session: Session,
        # headers: dict,
        http_session: ClientSession,
        session_manager: SessionManager,
        session_name: str,  # unique session name for this consumer instance
        my_logger: Logger = Provide[LoggerContainer.logger],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        # queue_changelog: asyncio.Queue = Provide[QueueContainer.queue_changelog],
):
    my_logger.debug(f"Started consume_changelog {name}")

    # Create dedicated session for this consumer through session manager
    pg_async_session = await session_manager.create_session_for_consumer(session_name)
    while True:
        try:
            async with debug_queue_operation(queue_changelog, "get", "queue_changelog") as item:
                # df_changelog: pd.DataFrame = await queue_changelog.get()
                df_changelog: pd.DataFrame = item

                if df_changelog is None:
                    my_logger.debug(f"{name} Found None in consume_changelog. QSize = {queue_changelog.qsize()}")
                    break
                if df_changelog.shape[0] > 0:
                        df_changelog = df_changelog.join(
                            pd.json_normalize(df_changelog.changelog)
                        ).drop(columns=["changelog"])
                        df_changelog.rename(columns={'id': 'issue_id', 'key': 'issue_key'}, inplace=True)

                        changelog_record: list = df_changelog.query("total > maxResults")[
                            ['issue_id', 'issue_key', 'total']].to_dict(orient='records')
                        my_logger.debug(f"{name} changelog_record = {len(changelog_record)}")
                        if len(changelog_record) > 0:
                            async def fetch_changelog_data(issue_id, issue_key, start_at):
                                asyncio.current_task().set_name("fetch_changelog_data")
                                try:
                                    changelog = await fetch_changelog(http_session, issue_key, params={'startAt': start_at})
                                    return {
                                        "issue_id": issue_id, "issue_key": issue_key,
                                        'startAt': changelog['startAt'],
                                        'maxResults': changelog['maxResults'],
                                        'total': changelog['total'],
                                        "histories": changelog['values']
                                    }
                                except ClientResponseError as err:
                                    handle_exception(err)
                                finally:
                                    my_logger.info(f"queue_changelog Queue size: {queue_changelog.qsize()}")

                            # changelog end point returns changelogs in ascending order
                            # issue search returns changelogs in descending order.
                            # Therefore, start range needs to be 0 and end range needs to be total records - 100

                            tasks_changelog = [
                                fetch_changelog_data(record['issue_id'], record['issue_key'], start_at)
                                for record in changelog_record
                                for start_at in range(0, record['total'] - 100, 100)
                            ]

                            changelog_results = await asyncio.gather(*tasks_changelog)

                            df = pd.DataFrame(changelog_results)

                            df_changelog = pd.concat([df_changelog, df], ignore_index=True)

                        df_changelog = df_changelog.explode(column="histories")
                        df_changelog.dropna(subset=["histories"], inplace=True)

                        df_changelog.histories = df_changelog.apply(
                            lambda x: add_issue_key_issue_id(x['histories'], x['issue_key'], x['issue_id']), axis=1
                        )

                        df_changelog.drop(columns=["startAt", "maxResults", "total", "issue_id", "issue_key"], inplace=True)
                        df_changelog = pd.json_normalize(df_changelog.histories)[
                            ["id", "created", "author.accountId", "items", "issue_key", "issue_id"]]

                        df_changelog.rename(columns={"author.accountId": "author"}, inplace=True)

                        # df_changelog[['id', 'issue_id']] = df_changelog[['id', 'issue_id']].astype(pd.Int64Dtype())
                        df_changelog = cast_columns(df_changelog, ['id', 'issue_id'], pd.Int64Dtype())
                        df_changelog = cast_columns(df_changelog, ['created'], "datetime")

                        # df_changelog['created'] = pd.to_datetime(df_changelog['created'])

                        # upsert_single(
                        #     pg_session, ChangelogJSON, df_changelog,
                        #     primary_key="id", on_conflict_update=True,
                        #     no_update_cols=("items",)
                        # )
                        # my_logger.debug(f"{name} upsert single")
                        # upsert(
                        #     pg_session, ChangelogJSON, df_changelog, primary_key="id",
                        #     on_conflict_update=False, no_update_cols=("items",)
                        # )
                        my_logger.debug(f"{name} begin upsert async")
                        # Removed lock since each consumer has its own session
                        _ = await upsert_async_with_health_check(
                            pg_async_session, ChangelogJSON, df_changelog,
                            on_conflict_update=False,
                            no_update_cols=("items",),
                            my_logger=my_logger
                        )

                        # Note: Commit will be handled by session_manager after all processing is complete
                        my_logger.debug(f"{name} end upsert async")

        finally:
            async with debug_queue_operation(queue_changelog, "task_done", "queue_changelog"):
                # queue_changelog.task_done()
                pass
            my_logger.debug(f"Completed consume_changelog {name}. QSize = {queue_changelog.qsize()}")


@inject
async def consume_worklog(
        queue_id: int,
        name: str,
        queue_worklog,
        project_key,
        # pg_session,
        # headers: dict,
        http_session: ClientSession,
        session_manager: SessionManager,
        session_name: str,  # unique session name for this consumer instance
        my_logger: Logger = Provide[LoggerContainer.logger],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        # queue_worklog: asyncio.Queue = Provide[QueueContainer.queue_worklog],
):
    # Create dedicated session for this consumer through session manager
    pg_async_session = await session_manager.create_session_for_consumer(session_name)

    while True:
        try:
            async with debug_queue_operation(queue_worklog, "get", "queue_worklog") as item:
                # df_worklog: pd.DataFrame | None = await queue_worklog.get()
                df_worklog: pd.DataFrame | None = item

                if df_worklog is None:
                    my_logger.debug(f"{name} breaking from loop qsize: {queue_worklog.qsize()}")
                    # queue_worklog.task_done()
                    break
                my_logger.debug(f"worklog size = {df_worklog.shape[0]}")
                df_worklog = df_worklog.join(
                    pd.json_normalize(df_worklog.worklog)
                ).drop(columns=["worklog"])

                df_worklog.query("total > 0", inplace=True)

                if df_worklog.shape[0] > 0:
                    worklog_keys: list = df_worklog.query("total > maxResults")[['key', 'id']].to_dict(
                        orient='records')
                    my_logger.debug(f"worklog keys = {worklog_keys}")
                    df_worklog.drop(
                        columns=["startAt", "maxResults", "total"],
                        inplace=True
                    )
                    df_worklog.rename(
                        columns={"id": "issue_id", "key": "issue_key"},
                        inplace=True
                    )

                    if len(worklog_keys) > 0:
                        async def fetch_worklog_data(issue: Dict[str, str]):
                            task = asyncio.current_task()
                            task.set_name(f"fetch_worklog_data_name")

                            issue_key = issue['key']
                            issue_id = issue['id']

                            try:
                                response = await fetch_worklog(http_session, issue_key)
                                if response["success"]:
                                    worklogs = response["result"]
                                    return {"issue_key": issue_key, "issue_id": issue_id, "worklogs": worklogs}
                                else:
                                    exception = response["exception"]
                                    my_logger.error(f"Failed to fetch worklogs: {exception}")

                            except ClientResponseError as err:
                                handle_exception(err)
                            finally:
                                my_logger.info(f"queue_worklog Queue size: {queue_worklog.qsize()}")

                        tasks_worklog = [fetch_worklog_data(issue_key) for issue_key in worklog_keys]
                        try:
                            worklog_results = await asyncio.gather(*tasks_worklog, return_exceptions=True)

                            # for result in worklog_results:
                            #     if isinstance(result, Exception):
                            #         my_logger.debug(f"Task raised an exception: {result}")
                            # if not worklog_results:
                            #     my_logger.info(f"No worklogs found in {name}")
                            # else:
                            #     my_logger.debug(f"Worklog returned: {len(worklog_results)}")

                            if len(worklog_results) > 0:
                                df = pd.DataFrame(worklog_results)
                                df_worklog = pd.concat([df_worklog, df])


                        except ClientResponseError as e:
                            handle_exception(e)
                        except Exception as e:
                            handle_exception(e)

                    df_worklog = df_worklog.explode(column="worklogs").reset_index(drop=True)

                    df_worklog = df_worklog.join(
                        pd.json_normalize(df_worklog['worklogs'])
                    ).drop(columns=["worklogs"])
                    df_worklog = df_worklog[df_worklog['id'].notna() & (df_worklog['id'] != '')]

                    drop_columns_prefixes = [
                        "self", "issueId", "author.", "updateAuthor."
                    ]
                    drop_column_exception = {
                        "author.accountId", "updateAuthor.accountId",
                    }

                    df_worklog.drop(
                        columns=[
                            col for col in df_worklog.columns if any(map(col.startswith, drop_columns_prefixes))
                                                                 and col not in drop_column_exception
                        ], inplace=True
                    )

                    if 'comment.version' in df_worklog.columns:
                        df_worklog.loc[pd.notnull(df_worklog['comment.version']), 'comment'] = df_worklog.loc[
                            pd.notnull(df_worklog['comment.version'])].apply(
                            lambda x: {
                                "version": x["comment.version"],
                                "type": x["comment.type"],
                                "content": x["comment.content"]
                            },
                            axis=1
                        )
                        df_worklog['comment'] = df_worklog['comment'].apply(lambda x: x if pd.notnull(x) else None)

                        df_worklog.drop(
                            columns=["comment.version", "comment.type", "comment.content"],
                            inplace=True
                        )

                    df_worklog.rename(
                        columns={
                            "author.accountId": "author",
                            "updateAuthor.accountId": "updateauthor"
                        }, inplace=True
                    )
                    df_worklog = cast_columns(df_worklog, ['id', 'timeSpentSeconds', 'issue_id'], pd.Int64Dtype())
                    df_worklog = cast_columns(df_worklog, ['created', 'updated', 'started'], "datetime")

                    # df_worklog[['id', 'timeSpentSeconds', 'issue_id']] = df_worklog[
                    #     ['id', 'timeSpentSeconds', 'issue_id']
                    # ].astype(pd.Int64Dtype())

                    # Specify the date format
                    # date_format = '%Y-%m-%dT%H:%M:%S.%f%z'
                    #
                    # for col in ['created', 'updated', 'started']:
                    #     df_worklog[col] = pd.to_datetime(df_worklog[col], format=date_format)

                    # upsert(
                    #     pg_session, WorkLog, df_worklog, primary_key="id",
                    #     no_update_cols=("timeSpentHours",)
                    # )
                    my_logger.debug(f"{name} begin upsert async")
                    # Removed lock since each consumer has its own session
                    _ = await upsert_async_with_health_check(
                        pg_async_session, WorkLog, df_worklog,
                        no_update_cols=("timeSpentHours",),
                        my_logger=my_logger
                    )

                    # Note: Commit will be handled by session_manager after all processing is complete
                    my_logger.debug(f"{name} end upsert async")

        finally:
            async with debug_queue_operation(queue_worklog, "task_done", "queue_worklog"):
                # queue_worklog.task_done()
                pass
            my_logger.info(f"Worklog {name} is done!!!. Queue Size: {queue_worklog.qsize()}")




@inject
async def consume_comment(
        queue_id: int,
        name: str,
        queue_comment: Queue,
        project_key: str,
        # pg_session: Session,
        # headers: dict,
        http_session: ClientSession,
        session_manager: SessionManager,
        session_name: str,  # unique session name for this consumer instance
        my_logger: Logger = Provide[LoggerContainer.logger],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        # queue_comment: asyncio.Queue = Provide[QueueContainer.queue_comment],
):
    # Create dedicated session for this consumer through session manager
    pg_async_session = await session_manager.create_session_for_consumer(session_name)

    while True:
        try:
            async with debug_queue_operation(queue_comment, "get", "queue_comment") as item:
                # df_comment: pd.DataFrame = await queue_comment.get()
                df_comment: pd.DataFrame = item

                if df_comment is None:
                    break

                if df_comment.shape[0] > 0:
                    df_comment = df_comment.join(
                        pd.json_normalize(df_comment.comment)
                    ).drop(columns=["comment", "self"])
                    df_comment.query("total > 0", inplace=True)

                    if df_comment.shape[0] > 0:
                        comment_keys: list = df_comment.query("total > maxResults")[['key', 'id']].to_dict(
                            orient='records')

                        if len(comment_keys) > 0:
                            # Send these to async process
                            async def fetch_comments_data(issue: Dict[str, str]):
                                issue_key = issue['key']
                                issue_id = issue['id']

                                comments = await fetch_comments(http_session, issue_key)
                                return {"issue_key": issue_key, "issue_id": issue_id, "comments": comments}

                            tasks_comment = [fetch_comments_data(issue_key) for issue_key in comment_keys]
                            comment_results = await asyncio.gather(*tasks_comment)
                            df = pd.DataFrame(comment_results)
                            df = df.explode(column="comments").reset_index(drop=True)

                    if df_comment.shape[0] > 0:
                        df_comment.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
                        df_comment = df_comment.explode(column="comments").reset_index(drop=True)
                        try:
                            df_comment = df_comment.join(
                                pd.json_normalize(df_comment["comments"])
                                [
                                    [
                                        "id", "author.accountId", "updateAuthor.accountId",
                                        "created", "updated", "jsdPublic", "body.version", "body.type", "body.content"
                                    ]
                                ]
                            ).drop(columns=["comments", "maxResults", "total", "startAt"])
                        except KeyError as e:
                            exc_type, exc_value, exc_tb = sys.exc_info()
                            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                            my_logger.error(''.join(tb.format_exception_only()))
                            my_logger.error(e)

                        df_comment["body"] = df_comment.apply(
                            lambda x: {
                                "version": x.get("body.version", 1),
                                "type": x.get("body.type", "doc"),
                                "content": x.get("body.content", [])
                            }, axis=1
                        )
                        df_comment.drop(columns=["body.version", "body.type", "body.content"], inplace=True)

                        df_comment.rename(
                            columns={"author.accountId": "author", "updateAuthor.accountId": "updateAuthor"},
                            inplace=True
                        )
                        df_comment = cast_columns(df_comment, ['id', 'issue_id'], pd.Int64Dtype())
                        df_comment = cast_columns(df_comment, ['created', 'updated'], "datetime")

                        # df_comment[['id', 'issue_id']] = df_comment[['id', 'issue_id']].astype(pd.Int64Dtype())
                        # df_comment[['created', 'updated']] = df_comment[['created', 'updated']].apply(pd.to_datetime)

                        # upsert(
                        #     pg_session, IssueComments, df_comment,
                        #     primary_key="id",
                        # )
                        my_logger.debug(f"{name} begin upsert async")
                        # Removed lock since each consumer has its own session
                        _ = await upsert_async_with_health_check(
                            pg_async_session, IssueComments, df_comment,
                            conflict_condition=["updated"],
                            my_logger=my_logger
                        )

                        # Note: Commit will be handled by session_manager after all processing is complete
                        my_logger.debug(f"{name} end upsert async")
                # queue_comment.task_done()

        finally:
            async with debug_queue_operation(queue_comment, "task_done", "queue_comment"):
                # queue_comment.task_done()
                pass
            my_logger.debug(f"consume_comment {name} is done!!!!. Qsize {queue_comment.qsize()}")


@inject
async def consume_issue_links(
        queue_id: int,
        name: str,
        queue_issue_links: Queue,
        project_key: str,
        # pg_session: Session,
        session_manager: SessionManager,
        session_name: str,  # unique session name for this consumer instance
        my_logger: Logger = Provide[LoggerContainer.logger],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        # queue_issue_links: asyncio.Queue = Provide[QueueContainer.queue_issue_links],
):
    # Create dedicated session for this consumer through session manager
    pg_async_session = await session_manager.create_session_for_consumer(session_name)

    while True:
        try:
            async with debug_queue_operation(queue_issue_links, "get", "queue_issue_links") as item:
                # df_issue_links: pd.DataFrame | None = await queue_issue_links.get()
                df_issue_links: pd.DataFrame | None = item

                if df_issue_links is None:
                    my_logger.debug(f"{name} Found None consume_issue_links. break out of loop")
                    break

                df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)

                # Drop rows with empty issuelinks
                df_issue_links = df_issue_links[df_issue_links['issuelinks'].apply(lambda x: len(x) > 0)]

                if not df_issue_links.empty:
                    df_issue_links = df_issue_links.explode("issuelinks")
                    df_issue_links = pd.concat(
                        [
                            df_issue_links.drop(columns="issuelinks").reset_index(drop=True),
                            pd.json_normalize(
                                df_issue_links['issuelinks'],
                                max_level=0
                            ).drop(columns="self")
                        ], axis=1
                    )
                    for col in ['outwardIssue', 'inwardIssue']:
                        if col in df_issue_links.columns:
                            df_issue_links = df_issue_links.join(
                                pd.json_normalize(
                                    df_issue_links[col],
                                )[['id', 'key']].rename(
                                    columns={"id": f"{col}_id", "key": f"{col}_key"}
                                )
                            ).drop(columns=col)

                    # columns_to_cast = ['id', 'outwardIssue_id', 'inwardIssue_id', 'issue_id']
                    # existing_columns = [col for col in columns_to_cast if col in df_issue_links.columns]
                    # df_issue_links[existing_columns] = df_issue_links[existing_columns].astype(pd.Int64Dtype())
                    df_issue_links = cast_columns(
                        df_issue_links,
                        ['id', 'outwardIssue_id', 'inwardIssue_id', 'issue_id'],
                        pd.Int64Dtype()
                    )
                    my_logger.debug(f"{name} begin upsert async")
                    # Removed lock since each consumer has its own session
                    ret_value = await upsert_async_with_health_check(
                        pg_async_session, IssueLinks, df_issue_links,
                        my_logger=my_logger
                    )

                    # Note: Commit will be handled by session_manager after all processing is complete
                    my_logger.debug(f"{name} end upsert async")

        finally:
            async with debug_queue_operation(queue_issue_links, "task_done", "queue_issue_links"):
                # queue_issue_links.task_done()
                pass
            my_logger.info(f"{name} consume_issue_links is done. QSize: {queue_issue_links.qsize()}")


@inject
async def consume_issue(
        queue_id: int,
        name: str,
        queue_issue: Queue,
        project_key: str,
        session_manager: SessionManager,
        session_name: str,  # unique session name for this consumer instance
        my_logger: Logger = Provide[LoggerContainer.logger],
        app_container: DynamicContainer = Provide[ApplicationContainer],
):
    # Create dedicated session for this consumer through session manager
    pg_async_session = await session_manager.create_session_for_consumer(session_name)
    # pg_async_session = await session_manager.create_session_for_consumer("consume_issue")
    # my_logger.debug(f"Started consume_issue {name}")


    while True:

        try:
            async with debug_queue_operation(queue_issue, "get", "queue_issue") as item:
                # df: pd.DataFrame | None = await queue_issue.get()
                df: pd.DataFrame | None = item
                if df is None:
                    my_logger.debug(f"{queue_id} marking queue_issue done.")
                    break

                df.dropna(how='all', axis=1, inplace=True)

                if df.shape[0] > 0:
                    for col in [
                        'components', 'fixVersions', 'versions',
                    ]:
                        if col in df.columns:
                            df[col] = df[col].apply(
                                lambda x: [value['name'] for value in x] if isinstance(x, list) else None)

                        # Define a condition to check if 'description.type' is not NaN
                    if 'description.type' in df.columns:
                        condition = df['description.type'].notna()

                        df.loc[condition, 'description'] = df.loc[condition].apply(
                            lambda row:
                            {
                                'type': row['description.type'],
                                "version": row['description.version'],
                                "content": row['description.content']
                            },
                            axis=1
                        )
                    drop_columns_prefixes = [
                        'parent.',
                        'customfield_10001.',
                        'worklog.',
                        'comment.comments',
                        'description.', 'assignee.', 'customfield_10006.',
                        'customfield_10049.', 'reporter.',
                        "customfield_10056.",
                        "customfield_10071.",
                        "customfield_10078.",
                        'customfield_10146.',
                        "customfield_10179.", "issuetype.", "priority.",
                        "resolution.", 'status.',
                        'customfield_10092.'
                    ]
                    exceptions = {
                        'parent.id', 'parent.key', 'assignee.accountId', 'customfield_10006.value',
                        'customfield_10001.name',
                        'customfield_10049.value', 'reporter.accountId',
                        "customfield_10056.value",
                        "customfield_10071.value",
                        "customfield_10078.value",
                        'customfield_10146.value',
                        "customfield_10179.value", 'issuetype.name', 'issuetype.subtask', 'issuetype.hierarchyLevel',
                        'priority.name',
                        'resolution.name', 'status.name', 'status.statusCategory.name',
                        'customfield_10092.value'
                    }

                    df.drop(
                        columns=[
                            col for col in df.columns if any(map(col.startswith, drop_columns_prefixes))
                                                         and col not in exceptions
                        ],

                        inplace=True
                    )

                    # col_prefix_rename = [
                    #     'aggregateprogress.'
                    # ]

                    column_rename_map = {
                        'aggregateprogress.percent': 'aggregateprogress_percent',
                        'aggregateprogress.progress': 'aggregateprogress_progress',
                        'aggregateprogress.total': 'aggregateprogress_total',
                        'progress.percent': 'progress_percent',
                        'progress.progress': 'progress_progress',
                        'progress.total': 'progress_total',
                        'assignee.accountId': 'assignee', 'reporter.accountId': "reporter",
                        "customfield_10006.value": "change_risk",
                        'customfield_10049.value': "severity",
                        'customfield_10015': 'startdate',
                        'customfield_10019': 'Rank',
                        "customfield_10056.value": "category_type",
                        "customfield_10071.value": "initiated_by",
                        "customfield_10078.value": 'approvalstatus',
                        'customfield_10146.value': 'reqfinalized',
                        "customfield_10179.value": "qc_check",
                        'customfield_10059': 'testcaseno',
                        'customfield_10060': 'testcasesuite',
                        'customfield_10061': 'teststepno',
                        'customfield_10062': 'scenariono',
                        'customfield_10067': 'ClientJira',
                        'customfield_10120': 'totaleffort',
                        'customfield_10121': 'totaldeveffort',
                        'customfield_10122': 'baeffort',
                        'customfield_10123': 'adeffort',
                        'customfield_10124': 'rdeffort',
                        'customfield_10125': 'qaeffort',
                        'customfield_10126': 'contingency',
                        "customfield_10147": "reopen_count",
                        "customfield_10256": "initiative_detail",
                        'issuetype.name': 'issuetype', 'priority.name': 'priority',
                        'resolution.name': "resolution", 'status.name': "status",
                        'issuetype.subtask': 'isSubTask',
                        'status.statusCategory.name': 'statusCategory',
                        'parent.id': 'parent_id',
                        'parent.key': 'parent_key',
                        "customfield_10199": "cvss_score",
                        'customfield_10024': 'storypoints',
                        'customfield_10020': 'sprint',
                        'customfield_10092.value': 'urgency'

                    }

                    # Update specific mappings if needed
                    column_rename_map.update({
                        'customfield_10001.name': 'Team',
                        'issuetype.hierarchyLevel': 'issue_hierarchy_level'
                    })

                    df.rename(columns=column_rename_map, inplace=True)

                    df_initiative_attribute = df[df['issuetype'] == "Initiative"].copy()
                    my_logger.debug(f"{queue_id} df_initiative_attribute size = {df_initiative_attribute.shape[0]}")

                    if df_initiative_attribute.shape[0] > 0:
                        required_columns = [
                            'id', 'key', 'customfield_10182', 'customfield_10183', 'customfield_10184',
                            'created', 'updated'
                        ]

                        # Check if the required columns are present in the DataFrame
                        missing_columns = [col for col in required_columns if
                                           col not in df_initiative_attribute.columns]

                        # Add missing columns with NaN values if they are not present
                        for col in missing_columns:
                            df_initiative_attribute[col] = float('nan')

                        df_initiative_attribute = df_initiative_attribute[required_columns]

                        df_initiative_attribute.rename(
                            columns={
                                'id': 'initiative_id',
                                'key': 'initiative_key',
                                'customfield_10182': 'project',
                                'customfield_10183': 'release',
                                'customfield_10184': 'feature'
                            }, inplace=True
                        )

                        df_initiative_attribute = cast_columns(df_initiative_attribute, ['initiative_id'],
                                                               pd.Int64Dtype())

                        # df_initiative_attribute['initiative_id'] = (
                        #     df_initiative_attribute['initiative_id'].astype(pd.Int64Dtype())
                        # )

                        # List of columns to drop
                        columns_to_drop = ["customfield_10182", "customfield_10183", "customfield_10184"]

                        # Check which columns actually exist in the DataFrame
                        existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]

                        # Drop only the existing columns
                        if existing_columns_to_drop:
                            df.drop(columns=existing_columns_to_drop, inplace=True)

                    df = cast_columns(
                        df,
                        [
                            'adeffort', 'aggregateprogress_percent', 'aggregateprogress_progress',
                            'aggregateprogress_total',
                            'aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent', 'baeffort',
                            'contingency', 'cvss_score', 'progress_percent', 'progress_progress', 'progress_total',
                            'qaeffort',
                            'rdeffort', 'timeestimate', 'timeoriginalestimate', 'timespent', 'totaldeveffort',
                            'totaleffort'

                        ],
                        pd.Float64Dtype()
                    )
                    # for col in columns_to_fill:
                    #     df[col] = df[col].astype(float)

                    # bigint

                    # df['id'] = df['id'].astype(int)
                    # df['parent_id'] = df['parent_id'].astype(pd.Int64Dtype())

                    # columns_to_fill = ['id', 'parent_id', 'reopen_count', 'storypoints', 'issue_hierarchy_level']
                    # # Filter columns to include only those present in the DataFrame
                    # columns_to_fill = [col for col in columns_to_fill if col in df.columns]
                    # df[columns_to_fill] = df[columns_to_fill].astype(pd.Int64Dtype())
                    df = cast_columns(
                        df,
                        ['id', 'parent_id', 'reopen_count', 'storypoints', 'issue_hierarchy_level'],
                        pd.Int64Dtype()
                    )

                    df['description_markdown'] = df['description_markdown'].apply(
                        lambda x: markdownify.markdownify(x, heading_style='ATX')
                    )

                    timetracking_columns = [
                        'timetracking.timeSpent',
                        'timetracking.remainingEstimate',
                        'timetracking.originalEstimate',
                        'timetracking.timeSpentSeconds',
                        'timetracking.remainingEstimateSeconds',
                        'timetracking.originalEstimateSeconds'
                    ]

                    missing_columns = [col for col in timetracking_columns if col not in df.columns]
                    if missing_columns:
                        my_logger.debug(f"Missing columns: {missing_columns}")

                    for col in missing_columns:
                        df[col] = None  # or any default value

                    df['timetracking'] = df[timetracking_columns].apply(
                        lambda row: {} if row.isna().all() else {
                            timetracking.split('.')[1]: row[timetracking]
                            for timetracking in timetracking_columns
                            if pd.notna(row[timetracking]) and row[timetracking] is not None

                        },
                        axis=1
                    )

                    # Drop the original timetracking columns
                    df.drop(columns=timetracking_columns, inplace=True)

                    # Convert the 'timetracking' column to JSON format
                    # df['timetracking'] = df['timetracking'].apply(json.dumps)

                    # REFERENCE CODE
                    # Define a wrapper function to call `upsert`
                    # async def run_upsert():
                    #     if df.shape[0] > 0:
                    #         # noinspection PyTypeChecker
                    #         await asyncio.to_thread(
                    #             upsert_single,
                    #             pg_session, Issue, df, primary_key="id",
                    #             no_update_cols=("tscv_summary_description",),
                    #             on_conflict_update=True
                    #         )
                    #
                    #     if df_initiative_attribute.shape[0] > 0:
                    #         my_logger.debug(f"doing upsert {df_initiative_attribute.shape[0]}")
                    #         # noinspection PyTypeChecker
                    #         await asyncio.to_thread(
                    #             upsert,
                    #             pg_session, InitiativeAttribute, df_initiative_attribute, primary_key="initiative_id",
                    #             no_update_cols=("attr",),
                    #             on_conflict_update=True
                    #         )
                    #     return

                    # Call the wrapper function with `await`
                    # await run_upsert()
                    # End Reference code

                    # List of columns to convert to datetime
                    datetime_columns = ['statuscategorychangedate', 'resolutiondate', 'created', 'updated']

                    # Filter columns to include only those present in the DataFrame
                    datetime_columns = [col for col in datetime_columns if col in df.columns]

                    # Apply pd.to_datetime with errors='coerce' to each column
                    df[datetime_columns] = df[datetime_columns].apply(pd.to_datetime, errors='coerce')

                    for col in ['startdate', 'duedate']:
                        if col in df.columns:
                            df[col] = pd.to_datetime(df[col], errors="coerce").dt.date


                    # Removed lock since each consumer has its own session
                    my_logger.debug(f"{name} begin upsert async issue")
                    ret_value = await upsert_async_with_health_check(
                        pg_async_session, Issue, df,
                        no_update_cols=("tscv_summary_description",),
                        on_conflict_update=True,
                        conflict_condition=["updated"],
                        my_logger=my_logger
                    )

                    # Note: Commit will be handled by session_manager after all processing is complete
                    my_logger.debug(f"{name} end upsert async issue")

                    if df_initiative_attribute.shape[0] > 0:
                        conflict_condition = and_(
                            InitiativeAttribute.updated < insert(InitiativeAttribute.__table__).excluded.updated
                        )
                        df_initiative_attribute['created'] = pd.to_datetime(df_initiative_attribute['created'])
                        df_initiative_attribute['updated'] = pd.to_datetime(df_initiative_attribute['updated'])

                        # Removed lock since each consumer has its own session
                        my_logger.debug(f"{name} begin upsert async initiative_attribute")
                        ret_value = await upsert_async_with_health_check(
                            pg_async_session, InitiativeAttribute, df_initiative_attribute,
                            no_update_cols=("attr",),
                            on_conflict_update=True,
                            conflict_condition=conflict_condition,
                            my_logger=my_logger
                        )

                        # Note: Commit will be handled by session_manager after all processing is complete
                        my_logger.debug(f"{name} end upsert async initiative_attribute")
                    my_logger.debug(f"result InitiativeAttribute = {ret_value}")

        finally:
            async with debug_queue_operation(queue_issue, "task_done", "queue_issue"):
                pass
                # queue_issue.task_done()

            my_logger.debug(f"consume_issue {name} is done. Queue size queue_issue: {queue_issue.qsize()}")


@inject
def handle_exception(e, my_logger: Logger = Provide[LoggerContainer.logger]):
    exc_type, exc_value, exc_tb = sys.exc_info()
    line_num = exc_tb.tb_lineno
    tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
    my_logger.exception(
        f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
        exc_info=True
    )
    raise e

@dataclass
class ConsumerConfig:
    """Configuration object to reduce parameter passing"""
    consumer_id: int
    project_key: str
    names: list
    name: str
    num_producers: int = 1


@dataclass
class QueueManager:
    """Encapsulates queue operations and naming"""
    q_container: any  # QueueContainer from dependency_injector
    project_key: str
    consumer_name: str

    def __post_init__(self):
        """Configure the queue container with the schema name"""
        self.q_container.config.override({"schema_name": self.project_key})

    def get_queues(self) -> dict:
        """Get the queue dictionary for the current schema"""
        return self.q_container.queue_selector()

    def get_task_name(self, task_type: str) -> str:
        """Generate standardized task names"""
        return f"{task_type}_{self.project_key}_{self.consumer_name.lower()}"

    async def send_termination_signals(self):
        """Send None signals to all queues to terminate consumers"""
        queues = self.get_queues()
        await queues["queue_changelog"].put(None)
        await queues["queue_worklog"].put(None)
        await queues["queue_comment"].put(None)
        await queues["queue_issue_links"].put(None)
        await queues["queue_issue"].put(None)


def normalize_issue_fields(df_issue: pd.DataFrame) -> pd.DataFrame:
    """Normalize fields and remove extracted data"""
    # Remove worklog and comment from fields after extraction
    df_issue['fields'] = df_issue['fields'].apply(
        lambda x: {k: v for k, v in x.items() if k not in ['worklog', 'comment']}
    )

    # Normalize fields into columns
    return df_issue.join(pd.json_normalize(df_issue.fields)).drop(columns=["fields"])


def process_description_markdown(df_issue: pd.DataFrame):
    """Extract description markdown from rendered fields"""
    df_issue['description_markdown'] = df_issue['renderedFields'].apply(
        lambda x: x.get('description', '') if isinstance(x, dict) else None
    )


def cleanup_issue_dataframe(df_issue: pd.DataFrame):
    """Remove unnecessary columns from issue dataframe"""
    columns_to_drop = [
        'changelog', 'expand', 'self', 'worklog', 'comment', 'renderedFields'
    ]
    existing_columns = [col for col in columns_to_drop if col in df_issue.columns]
    df_issue.drop(columns=existing_columns, inplace=True)


class IssueProcessor:
    """Handles issue data processing and distribution to queues"""

    def __init__(self, queue_manager: QueueManager, logger: Logger):
        self.queue_manager = queue_manager
        self.logger = logger

    async def process_issue_batch(self, issues: list):
        """Process a batch of issues and distribute to appropriate queues"""
        if not issues:
            return

        df_issue = pd.DataFrame(issues)

        # Extract and queue changelog data
        await self._process_changelog(df_issue)

        # Extract and queue worklog data
        await self._process_worklog(df_issue)

        # Extract and queue comment data
        await self._process_comment(df_issue)

        # Process main issue data
        df_issue = normalize_issue_fields(df_issue)

        # Extract and queue issue links
        await self._process_issue_links(df_issue)

        # Process description markdown
        process_description_markdown(df_issue)

        # Clean up issue dataframe
        cleanup_issue_dataframe(df_issue)

        # Queue the processed issue data
        queues = self.queue_manager.get_queues()
        await queues["queue_issue"].put(df_issue)

        self.logger.debug(f"Processed {df_issue.shape[0]} records")

    async def _process_changelog(self, df_issue: pd.DataFrame):
        """Extract and queue changelog data"""
        df_changelog = df_issue[["id", "key", 'changelog']].copy()
        queues = self.queue_manager.get_queues()
        await queues["queue_changelog"].put(df_changelog)

    async def _process_worklog(self, df_issue: pd.DataFrame):
        """Extract and queue worklog data"""
        df_issue['worklog'] = df_issue['fields'].apply(lambda x: x.get('worklog'))
        df_worklog = df_issue[['worklog', "id", "key"]].copy()
        queues = self.queue_manager.get_queues()
        await queues["queue_worklog"].put(df_worklog)

    async def _process_comment(self, df_issue: pd.DataFrame):
        """Extract and queue comment data"""
        df_issue['comment'] = df_issue['fields'].apply(lambda x: x.get('comment'))
        df_comment = df_issue[['comment', "id", "key"]].copy()
        queues = self.queue_manager.get_queues()
        await queues["queue_comment"].put(df_comment)

    async def _process_issue_links(self, df_issue: pd.DataFrame):
        """Extract and queue issue links if they exist"""
        if 'issuelinks' not in df_issue.columns:
            return

        df_issue_links = df_issue[['issuelinks', "id", "key"]].copy()
        df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
        df_issue.drop(columns=['issuelinks'], inplace=True)

        # Filter out empty issuelinks
        df_issue_links = df_issue_links[
            df_issue_links['issuelinks'].apply(lambda x: len(x) > 0)
        ]

        if not df_issue_links.empty:
            queues = self.queue_manager.get_queues()
            await queues["queue_issue_links"].put(df_issue_links)


class ConsumerTaskManager:
    """Manages the creation and coordination of consumer tasks"""

    def __init__(self, config: ConsumerConfig, queue_manager: QueueManager,
                 http_session: ClientSession, logger: Logger, session_manager: SessionManager):
        self.config = config
        self.queue_manager = queue_manager
        self.http_session = http_session
        self.logger = logger
        self.session_manager = session_manager

    def create_consumer_tasks(self, task_group):
        """Create all consumer tasks with separate database sessions for each"""
        queues = self.queue_manager.get_queues()

        # Create unique session names for each consumer instance
        consumer_prefix = f"consumer_{self.config.consumer_id}"

        tasks = [
            ("consume_changelog", consume_changelog, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_changelog"),
                queues["queue_changelog"],
                self.config.project_key,
                self.http_session,
                self.session_manager,
                f"{consumer_prefix}_changelog",  # unique session name
            ]),
            ("consume_worklog", consume_worklog, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_worklog"),
                queues["queue_worklog"],
                self.config.project_key,
                self.http_session,
                self.session_manager,
                f"{consumer_prefix}_worklog",  # unique session name
            ]),
            ("consume_comment", consume_comment, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_comment"),
                queues["queue_comment"],
                self.config.project_key,
                self.http_session,
                self.session_manager,
                f"{consumer_prefix}_comment",  # unique session name
            ]),
            ("consume_issue_links", consume_issue_links, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_issue_links"),
                queues["queue_issue_links"],
                self.config.project_key,
                self.session_manager,
                f"{consumer_prefix}_issue_links",  # unique session name
            ]),
            ("consume_issue", consume_issue, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_issue"),
                queues["queue_issue"],
                self.config.project_key,
                self.session_manager,
                f"{consumer_prefix}_issue",  # unique session name
            ]),
        ]

        for task_type, function_name, args in tasks:
            task_group.create_task(
                function_name(*args),
                name=self.queue_manager.get_task_name(task_type)
            )


class TerminationController:
    """Handles the termination logic for consumers"""

    def __init__(self, num_producers: int, queue_manager: QueueManager, logger: Logger):
        self.num_producers = num_producers
        self.queue_manager = queue_manager
        self.logger = logger
        self.none_count = 0

    async def handle_none_signal(self, consumer_id: int, name: str) -> bool:
        """Handle None signal and return True if it should terminate"""
        self.none_count += 1
        queues = self.queue_manager.get_queues()

        self.logger.debug(
            f"None found in {name} None count: {self.none_count}/{self.num_producers}. "
            f"QSize = {queues['queue_issues'].qsize()}"
        )

        # Wait for all producers to send their None signals
        # Using the same logic as original: count_none == 2 (hardcoded check)
        if self.none_count == 2:  # Keeping original logic
            self.logger.info(f"Consumer {consumer_id} sending termination signals to all queues")
            await self.queue_manager.send_termination_signals()
            return True

        return False


async def _process_issues_loop(
        queue_manager: QueueManager,
        issue_processor: IssueProcessor,
        termination_controller: TerminationController,
        consumer_id: int,
        name: str,
        logger: Logger
):
    """Separated main processing loop for clarity with graceful shutdown support"""
    queues = queue_manager.get_queues()
    queue_issues_task_done = 0

    logger.debug("Starting main processing loop")

    while True:
        try:
            # Check for graceful shutdown request before processing
            if is_shutdown_requested():
                logger.warning(f"Consumer {consumer_id} & {name}: Graceful shutdown requested, exiting loop")
                break

            # Debug queue get operation
            # async with debug_queue_operation(queues["queue_issues"], "get", "queue_issues") as item:
            #     issue_batch: Optional[list] = item

            issue_batch: Optional[list] = await queues["queue_issues"].get()
            queue_issues_task_done += 1

            if issue_batch is None:
                logger.debug(f"{consumer_id} & {name}: Found None in queue_issues. QSize = {queues['queue_issues'].qsize()}")
                should_terminate = await termination_controller.handle_none_signal(
                    consumer_id, name
                )
                if should_terminate:
                    logger.debug(f"Consumer {consumer_id} & {name} breaking from loop")
                    logger.debug(f"queue_issues_task_done = {queue_issues_task_done}")
                    logger.debug(f"queue_issues QSize = {queues['queue_issues'].qsize()}")
                    break
                continue

            # Process the issue batch
            await issue_processor.process_issue_batch(issue_batch)

        except GracefulShutdownRequested as e:
            logger.critical(f"Consumer {consumer_id} & {name}: Graceful shutdown requested: {e}")
            # Don't re-raise, just break the loop to allow clean termination
            break
        except Exception as e:
            logger.error(f"Consumer {consumer_id} & {name}: Error in processing loop: {e}", exc_info=True)
            # Check if this is a critical error that should trigger shutdown
            if isinstance(e, (DatabaseConnectionError, MaxRetriesExceededError)):
                logger.critical(f"Consumer {consumer_id} & {name}: Critical error, requesting graceful shutdown")
                request_graceful_shutdown(f"Critical error in consumer {consumer_id}: {e}", logger)
                break
            # For other errors, continue processing
            handle_exception(e)
        finally:
            logger.debug(f"{name} Queue size queue_issues: {queues['queue_issues'].qsize()}")
            queues["queue_issues"].task_done()
            queue_issues_task_done -= 1
            logger.debug(f"queue_issues_task_done = {queue_issues_task_done}")
            # Debug task_done operation
            # async with debug_queue_operation(queues["queue_issues"], "task_done", "queue_issues"):
            #     pass

    logger.info(f"Consumer {consumer_id} & {name}: Processing loop completed")

@inject
# @debug_async_function("consume_issues")
async def consume_issues(
        consumer_id: int,
        project_key: str,
        http_session: ClientSession,
        names: list,
        name: str,
        global_session_manager: SessionManager,
        num_producers: int = 1,
        my_logger: Logger = Provide[LoggerContainer.logger],
        q_container: DynamicContainer = Provide[QueueContainer],
        app_container: DynamicContainer = Provide[ApplicationContainer],
):
    """Main consumer function - focused on orchestration with graceful shutdown support"""
    my_logger.debug(f"consumer_id {consumer_id}, name={names[consumer_id]}")

    try:
        # Check for shutdown request before starting
        if is_shutdown_requested():
            my_logger.warning(f"Consumer {consumer_id}: Shutdown already requested, exiting early")
            return

        # Create configuration and supporting objects
        config = ConsumerConfig(consumer_id, project_key, names, name, num_producers)

        # Create supporting objects - QueueManager will handle config override
        queue_manager = QueueManager(q_container, project_key, names[consumer_id])
        issue_processor = IssueProcessor(queue_manager, my_logger)

        # Use the global session manager passed from process_jira_issues
        task_manager = ConsumerTaskManager(
            config, queue_manager, http_session, my_logger, global_session_manager
        )
        termination_controller = TerminationController(num_producers, queue_manager, my_logger)

        async with asyncio.TaskGroup() as tg:
            try:
                # Create all consumer tasks
                task_manager.create_consumer_tasks(tg)

                # Main processing loop with graceful shutdown handling
                await _process_issues_loop(
                    queue_manager, issue_processor, termination_controller,
                    consumer_id, name, my_logger
                )

                my_logger.debug(f"{project_key} Consumer {consumer_id} processing completed!")

            except* GracefulShutdownRequested as gsr:
                my_logger.critical(f"Consumer {consumer_id}: Graceful shutdown requested during task group execution")
                # Don't re-raise, allow clean termination
                for error in gsr.exceptions:
                    my_logger.critical(f"Shutdown reason: {error}")
            except* TypeError as te:
                for error in te.exceptions:
                    my_logger.error(f"TypeError in consumer {consumer_id}: {error}")
            except* Exception as ex:
                my_logger.error(f"Exception in consumer {consumer_id}: {ex.exceptions}")
                # Check if any of the exceptions are critical
                for error in ex.exceptions:
                    error_msg = str(error).lower()

                    # Handle failed transaction errors specifically
                    if ('current transaction is aborted' in error_msg or
                        'infailedsqltransactionerror' in error_msg):
                        my_logger.critical(f"Failed transaction detected in consumer {consumer_id}, rolling back sessions")
                        try:
                            await global_session_manager.rollback_all_sessions()
                            my_logger.info(f"Successfully rolled back all sessions for consumer {consumer_id}")
                        except Exception as rollback_error:
                            my_logger.error(f"Failed to rollback sessions in consumer {consumer_id}: {rollback_error}")

                        request_graceful_shutdown(f"Failed transaction in consumer {consumer_id}: {error}", my_logger)
                        break
                    # Handle concurrent operation errors
                    elif ('another operation is in progress' in error_msg or
                          'interfaceerror' in error_msg):
                        my_logger.warning(f"Concurrent operation error in consumer {consumer_id}, attempting recovery")
                        try:
                            await asyncio.sleep(1.0)  # Brief pause to let operations complete
                            await global_session_manager.rollback_all_sessions()
                            my_logger.info(f"Successfully handled concurrent operation error for consumer {consumer_id}")
                        except Exception as recovery_error:
                            my_logger.error(f"Failed to recover from concurrent operation error in consumer {consumer_id}: {recovery_error}")
                            request_graceful_shutdown(f"Concurrent operation recovery failed in consumer {consumer_id}: {error}", my_logger)
                            break
                    # Handle lock timeout errors
                    elif ('locknotavailableerror' in error_msg or
                          'lock timeout' in error_msg):
                        my_logger.warning(f"Lock timeout error in consumer {consumer_id}, this may resolve automatically")
                        # Lock timeouts are often transient, don't immediately shutdown
                        continue
                    elif isinstance(error, (DatabaseConnectionError, MaxRetriesExceededError)):
                        my_logger.critical(f"Critical error in consumer {consumer_id}, requesting shutdown")
                        request_graceful_shutdown(f"Critical error in consumer {consumer_id}: {error}", my_logger)
                        break
            finally:
                my_logger.debug(f"consume_issues {consumer_id} task group completed")

        # Commit any pending transactions after successful completion
        try:
            await global_session_manager.commit_all_sessions()
            my_logger.debug(f"Consumer {consumer_id}: Successfully committed all sessions")
        except Exception as e:
            error_msg = str(e).lower()
            my_logger.error(f"Consumer {consumer_id}: Error committing sessions: {e}", exc_info=True)

            # Handle different types of commit errors
            if 'current transaction is aborted' in error_msg or 'infailedsqltransactionerror' in error_msg:
                my_logger.warning(f"Consumer {consumer_id}: Failed transaction detected during commit, attempting rollback")
                try:
                    await global_session_manager.rollback_all_sessions()
                    my_logger.info(f"Consumer {consumer_id}: Successfully rolled back failed transactions")
                except Exception as rollback_error:
                    my_logger.error(f"Consumer {consumer_id}: Failed to rollback after commit error: {rollback_error}")
            elif 'another operation is in progress' in error_msg or 'interfaceerror' in error_msg:
                my_logger.warning(f"Consumer {consumer_id}: Concurrent operation detected during commit")
                # For concurrent operation errors during commit, try a brief retry
                try:
                    await asyncio.sleep(1.0)  # Brief pause
                    await global_session_manager.commit_all_sessions()
                    my_logger.info(f"Consumer {consumer_id}: Successfully committed after concurrent operation retry")
                    return  # Success, don't request shutdown
                except Exception as retry_error:
                    my_logger.error(f"Consumer {consumer_id}: Retry commit failed: {retry_error}")
                    try:
                        await global_session_manager.rollback_all_sessions()
                        my_logger.info(f"Consumer {consumer_id}: Rolled back after failed retry")
                    except Exception as rollback_error:
                        my_logger.error(f"Consumer {consumer_id}: Rollback after retry failed: {rollback_error}")

            # This is a critical error - data might be lost
            request_graceful_shutdown(f"Failed to commit sessions in consumer {consumer_id}: {e}", my_logger)

    except GracefulShutdownRequested:
        my_logger.critical(f"Consumer {consumer_id}: Graceful shutdown requested")
        # Don't re-raise to allow clean termination
    except Exception as e:
        my_logger.error(f"Consumer {consumer_id}: Unexpected error: {e}", exc_info=True)
        # For unexpected errors, request graceful shutdown
        request_graceful_shutdown(f"Unexpected error in consumer {consumer_id}: {e}", my_logger)

    my_logger.info(f"{consumer_id} {name} consume_issues finished!")


@inject
async def get_issues_from_jira_jql(
        project_key: str,
        scope: str,
        batch_start_time,
        http_session: ClientSession, base_url: str, url: str, name: str,
        jql: str, total_records: int, producer_count: int,
        my_logger: Logger = Provide[LoggerContainer.logger],
        q_container: DynamicContainer = Provide[QueueContainer],
        fields_container: FieldNameExtractor = Provide[IssueFieldsContainer.field_name_extractor]
):
    global commit_transaction
    q_container.config.override({"schema_name": f"{project_key}"})
    my_logger.info(f"Called get_issues_from_jira_jql")


    fields: list = fields_container.get_field_names()

    loop_count = 0

    try:
        payload = {
            "fields": fields,
            "fieldsByKeys": True,
            "jql": jql,
            "maxResults": 100,
            "expand": "changelog,renderedFields"
        }

        while True:
            response = await fetch_with_retries_post(http_session, url, payload)

            if response['success']:
                record_counts = len(response['result']['issues'])
                if record_counts > 0:
                    await q_container.queue_selector()["queue_issues"].put(response['result']['issues'])
                    await q_container.queue_selector()["queue_stats"].put(
                        {
                            'isLast': False,
                            'process_time': 0,
                            'elapsed_time': 0,
                            'record_count': record_counts,
                            'total': total_records,
                            'producer_id': 0,
                            'name': name,
                            'producer_count': producer_count
                        }
                    )

                if not response['result'].get('nextPageToken'):
                    my_logger.info("nextPageToken not found. Exiting!!")
                    break
                else:
                    next_page_token = response['result'].get('nextPageToken')
                    payload['nextPageToken'] = next_page_token

            loop_count += 1
        my_logger.info(f"Completed get_issues_from_jira_jql")

    except Exception as e:
        handle_exception(e)
    finally:
        iterations = max(1, 10 // producer_count)
        for i in range(iterations):
            my_logger.debug(f"{i+1}. Adding None end of queue signal to queue_issues & queue_stats")
            await q_container.queue_selector()["queue_issues"].put(None)
            await q_container.queue_selector()["queue_stats"].put(
                {
                    'isLast': True,
                    'process_time': 0,
                    'elapsed_time': 0,
                    'record_count': record_counts,
                    'total': total_records,
                    'name': name,
                    'producer_count': producer_count
                }
            )
        my_logger.info(f"Completed get_issues_from_jira")


@inject
async def get_version_related_details(
        session, url, semaphore: asyncio.locks.Semaphore,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    task = asyncio.current_task()
    task.set_name("get_version_related_details")

    async with semaphore:
        async with session.request(
                "GET",
                url,
        ) as resp:
            try:
                if resp.status == 200:

                    response = await resp.json()
                    if 'relatedwork' in url:
                        for item in response:
                            item['self'] = url
                    return {"success": True, "result": response}

                else:
                    my_logger.error(f"Request to {url} failed with status {resp.status}")
                    return {"success": False, "exception": f"HTTP {resp.status}"}

            except Exception as e:
                my_logger.error(f"Error processing response from {url}: {e}")
                return {"success": False, "exception": e}


@inject
async def process_jira_versions(
        project_key: str,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],

):
    # config_dict = get_env_variables()
    my_logger.debug("Started process_jira_versions")
    semaphore = asyncio.Semaphore(30)
    asyncio.current_task().set_name(f"process_jira_versions_{project_key}")
    app_container.schema.override(project_key)

    async with (aiohttp.ClientSession(headers=jira_entry.custom_properties) as http_session):
        # response = await fetch_versions(http_session,  project_key.upper())

        response = await fetch_with_retries_get_new(
            http_session, url=f"{jira_entry.url}/rest/api/3/project/{project_key.upper()}/versions"
        )

        # Separate successes and exceptions
        successful_responses = response["result"] if response.get("success") else []
        exceptions = response["exception"] if not response.get("success") else []

        my_logger.debug(f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}")
        my_logger.debug(f"# of exceptions: {len(exceptions)}")

        if not exceptions:
            df = pd.DataFrame(successful_responses)
            df['relatedIssueCounts'] = df['self'] + '/relatedIssueCounts'
            df['relatedwork'] = df['self'] + '/relatedwork'
            df['unresolvedIssueCount'] = df['self'] + '/unresolvedIssueCount'
            ret_string = f'+{df.shape[0]}'
            try:
                task_related_issue_counts = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_relatedIssueCounts_{num2words(i, ordinal=True)}"
                    )
                    for i, row in enumerate(df['relatedIssueCounts'].tolist())
                ]

                task_related_work = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_relatedwork_{num2words(i, ordinal=True)}"
                    ) for i, row in enumerate(df['relatedwork'].tolist())
                ]

                task_unresolved_issue_count = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_unresolvedIssueCount_{num2words(i, ordinal=True)}"
                    ) for i, row in enumerate(df['unresolvedIssueCount'].tolist())
                ]

                # Start both sets of tasks concurrently
                tasks = task_related_issue_counts + task_related_work + task_unresolved_issue_count
                results = await asyncio.gather(*tasks)

                # Separate successful and failed responses
                successful_related_issue_counts = [
                    result["result"] for result in results[:len(task_related_issue_counts)] if result.get("success")
                ]
                successful_related_work = [
                    result["result"] for result in
                    results[len(task_related_issue_counts):len(task_related_issue_counts) + len(task_related_work)] if
                    result.get("success")
                ]
                successful_unresolved_issue_count = [
                    result["result"] for result in results[len(task_related_issue_counts) + len(task_related_work):] if
                    result.get("success")
                ]

                exceptions = [
                    result["exception"] for result in results if not result.get("success")
                ]

                # Log exceptions
                for exception in exceptions:
                    my_logger.error(f"Task failed with exception: {exception}")

                # Normalize successful responses into dataframes
                if successful_related_issue_counts:
                    df_related = pd.json_normalize(successful_related_issue_counts)
                else:
                    df_related = pd.DataFrame()

                if successful_related_work:
                    df_related_work = pd.json_normalize(
                        [item for sublist in successful_related_work for item in sublist])
                else:
                    df_related_work = pd.DataFrame()

                if successful_unresolved_issue_count:
                    df_unresolved_issue_count = pd.json_normalize(successful_unresolved_issue_count)
                else:
                    df_unresolved_issue_count = pd.DataFrame()

                # # Split the results back into their respective lists
                # task_related_issue_counts = results[:len(task_related_issue_counts)]
                # task_related_work = results[
                #                     len(task_related_issue_counts):len(task_related_issue_counts) +
                #                     len(task_related_work)
                #                     ]
                # task_unresolved_issue_count = results[len(task_related_issue_counts) + len(task_related_work):]
                #
                # # Filter out empty lists
                # filtered_data = [item for sublist in task_related_work if sublist for item in sublist if sublist]
                #
                # df_related = pd.json_normalize(task_related_issue_counts)
                # df_related_work = pd.json_normalize(filtered_data)
                # df_unresolved_issue_count = pd.json_normalize(task_unresolved_issue_count)

                for data_frame in [df_related, df_unresolved_issue_count]:
                    data_frame['id'] = data_frame['self'].apply(lambda x: x.split('/')[-1])
                    data_frame.drop(columns=["self"], inplace=True)
                if df_related_work.shape[0] > 0:
                    df_related_work['id'] = df_related_work['self'].apply(lambda x: x.split('/')[-2])
                    df_related_work.drop(columns=["self"], inplace=True)

                df.drop(
                    columns=[
                        'relatedIssueCounts', 'relatedwork', 'unresolvedIssueCount',
                        'self'
                    ],
                    inplace=True
                )
                df = pd.merge(df, df_related, how='left', on='id')
                df = pd.merge(df, df_unresolved_issue_count, how='left', on='id')
                if df_related_work.shape[0] > 0:
                    df = pd.merge(df, df_related_work, how='left', on='id')

                with app_container.database_rw().update_schema(project_key).session() as pg_session:
                    upsert(pg_session, Versions, df, )
                    pg_session.commit()
                return ret_string
            except Exception as e:
                handle_exception(e)
        else:
            for exception in exceptions:
                my_logger.error(f"Task failed with exception: {exception}")
            return "UNKNOWN EXCEPTION"


@inject
async def process_jira_issues(
        project_key: str,
        scope: str,
        initial_load: bool,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        q_container: DynamicContainer = Provide[QueueContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    """
    Process JIRA issues with improved error handling, transaction management,
    graceful shutdown support, and better separation of concerns.
    """
    # Create a global session manager for coordinated commits across all consumers
    global_session_manager = SessionManager(app_container, project_key)

    try:
        # Check for shutdown request before starting
        if is_shutdown_requested():
            my_logger.warning(f"Shutdown already requested, skipping processing for {project_key}")
            return

        # Initialize timezone and process marker
        time_zone, process_start_marker = await _initialize_jira_session(
            jira_entry, my_logger
        )

        my_logger.info(f"Processing project: {project_key}, scope: {scope}")

        # Setup database schema and get last run datetime
        app_container.schema.override(project_key)
        q_container.config.override({"schema_name": project_key})

        async with app_container.database_rw_enhanced().update_schema(project_key).async_session() as pg_session:
            # Check for shutdown request before database operations
            if is_shutdown_requested():
                my_logger.warning(f"Shutdown requested during initialization for {project_key}")
                return

            last_run_datetime = await _get_last_run_datetime(pg_session, time_zone, my_logger)

            # Build and execute JQL query
            jql = _build_jql_query(project_key, scope, last_run_datetime)
            if not jql:
                my_logger.warning(f"No valid JQL generated for scope: {scope}")
                return

            # Process issues with proper transaction handling and graceful shutdown support
            success = await _process_issues_with_producers_consumers(
                project_key=project_key,
                scope=scope,
                jql_query=jql,
                last_run_datetime=last_run_datetime,
                pg_session=pg_session,
                jira_entry=jira_entry,
                q_container=q_container,
                my_logger=my_logger,
                global_session_manager=global_session_manager
            )
            my_logger.info(f"process_jira_issues_with_producers_consumers completed for {project_key}")

            # Check for shutdown request before cleanup operations
            if is_shutdown_requested():
                my_logger.warning(f"Shutdown requested before cleanup for {project_key}")
                # Still attempt to rollback to maintain data integrity
                await global_session_manager.rollback_all_sessions()
                await pg_session.rollback()
                my_logger.info(f"Rolled back transactions due to shutdown request for {project_key}")
                return

            # Handle parent-child relationship cleanup
            await _cleanup_orphaned_parent_references(pg_session, my_logger)

            # Commit all consumer sessions in coordinated manner
            if success and not is_shutdown_requested():
                my_logger.info("Processing completed. Starting coordinated commit...")
                try:
                    await global_session_manager.commit_all_sessions()
                    my_logger.info("All consumer sessions committed successfully!")

                    # Commit the main session
                    await pg_session.commit()
                    my_logger.info(f"Main transaction committed successfully for {project_key}")

                    # Update run details for project scope
                    if scope == "project":
                        await _update_run_details(pg_session, process_start_marker, my_logger)

                except Exception as commit_error:
                    my_logger.error(f"Error during commit for {project_key}: {commit_error}", exc_info=True)
                    # This is a critical error - request graceful shutdown
                    request_graceful_shutdown(
                        f"Failed to commit transactions for {project_key}: {commit_error}",
                        my_logger
                    )
                    raise
            else:
                # Rollback all sessions on failure or shutdown request
                reason = "shutdown requested" if is_shutdown_requested() else "processing errors"
                my_logger.warning(f"Rolling back all transactions due to: {reason}")
                await global_session_manager.rollback_all_sessions()
                await pg_session.rollback()
                my_logger.info(f"All transactions rolled back for {project_key}")

    except GracefulShutdownRequested as gsr:
        my_logger.critical(f"Graceful shutdown requested during process_jira_issues for {project_key}: {gsr}")
        # Don't re-raise to allow clean termination
        try:
            # Attempt to rollback any pending transactions
            await global_session_manager.rollback_all_sessions()
            my_logger.info(f"Rolled back sessions due to graceful shutdown for {project_key}")
        except Exception as rollback_error:
            my_logger.error(f"Error during shutdown rollback for {project_key}: {rollback_error}")
    except Exception as e:
        my_logger.error(f"Fatal error in process_jira_issues for {project_key}: {str(e)}", exc_info=True)

        # Check if this is a critical error that should trigger shutdown
        if isinstance(e, (DatabaseConnectionError, MaxRetriesExceededError)):
            request_graceful_shutdown(f"Critical error in process_jira_issues for {project_key}: {e}", my_logger)
        else:
            handle_exception(e)
            raise
    finally:
        try:
            await global_session_manager.close_all_sessions()
            my_logger.info(f"Closed all sessions for {project_key}")
        except Exception as close_error:
            my_logger.error(f"Error closing sessions for {project_key}: {close_error}")

        my_logger.info(f"process_jira_issues completed for {project_key}")


async def _initialize_jira_session(jira_entry: EntryDetails, my_logger: Logger) -> tuple:
    """Initialize JIRA session and get timezone information."""
    endpoint = "/rest/api/3/myself"
    url = f"{jira_entry.url}{endpoint}"

    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(headers=jira_entry.custom_properties, timeout=timeout) as session:
        response = await fetch_with_retries_get_old(session, url, my_logger=my_logger)
        time_zone = ZoneInfo(response['timeZone'])
        process_start_marker = datetime.now(tz=time_zone)

    return time_zone, process_start_marker


async def _get_last_run_datetime(
        pg_session,
        time_zone: ZoneInfo,
        my_logger: Logger
) -> datetime:
    """Get the last run datetime or return default if not found."""
    stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Issue")

    try:
        result = await pg_session.execute(stmt)
        local_datetime = result.scalar_one()
        my_logger.info(f"Last run datetime: {local_datetime}")
        data_load.set("incremental")
        return local_datetime

    except sqlalchemy.exc.NoResultFound:
        # Default to a very old date for initial load
        default_datetime = datetime(2010, 1, 1, 0, 0, 0, tzinfo=time_zone)
        my_logger.debug(f"No previous run found, using default: {default_datetime}")
        data_load.set("initial")
        return default_datetime

    except Exception as e:
        my_logger.error(f"Error getting last run datetime: {str(e)}")
        raise


def _build_jql_query(project_key: str, scope: str, last_run_datetime: datetime) -> str:
    """Build JQL query based on scope and load type."""
    project_upper = project_key.upper()
    datetime_str = last_run_datetime.strftime('%Y-%m-%d %H:%M')

    if scope == "project":
        if data_load.get() == "initial":
            jql = f"""
                project = {project_upper} 
                AND created > '{datetime_str}' 
                ORDER BY created ASC
            """
        else:
            jql = f"""
                project = {project_upper} 
                AND updated > '{datetime_str}' 
                ORDER BY updated ASC
            """
    elif scope == "recon":
        jql = f"""
            project = {project_upper} 
            AND issuetype = 'Initiative'
        """
    else:
        return ""

    # Normalize whitespace
    return ' '.join(jql.split())


@debug_async_function("process_issues_with_producers_consumers")
async def _process_issues_with_producers_consumers(
        project_key: str,
        scope: str,
        jql_query: str,
        last_run_datetime: datetime,
        pg_session,
        jira_entry: EntryDetails,
        q_container: DynamicContainer,
        my_logger: Logger,
        global_session_manager: SessionManager
) -> Union[ bool|None]:
    """Process issues using producer-consumer pattern with proper error handling."""

    # Configuration
    CONSUMER_COUNT = 5
    FRUIT_NAMES = ["apple", "banana", "cherry", "dates", "fig"]

    my_logger.debug(f"JQL Query: {jql_query}")

    # Use timeout detection to catch stuck operations
    async with debug_timeout_detection(f"process_issues_{project_key}_{scope}", timeout_seconds=600):
        try:
            # Split JQL into multiple queries for parallel processing
            jql_queries, total_records_estimate = await split_jql_by_count(jql_query)

            if not jql_queries:
                my_logger.warning("No JQLs returned, using original query")
                jql_queries = [jql_query]
                total_records_estimate = -1

            num_producers = len(jql_queries)
            my_logger.info(f"Starting {num_producers} producers and {CONSUMER_COUNT} consumers")
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(headers=jira_entry.custom_properties, timeout=timeout) as http_session:
                async with asyncio.TaskGroup() as task_group:
                    # Start periodic stuck detection if debugging is enabled
                    if hasattr(debug_monitor, 'enabled') and debug_monitor.enabled:
                        task_group.create_task(
                            periodic_stuck_check(),
                            name="stuck_detector"
                        )

                    # Create producer tasks
                    for task_id, jql_query in enumerate(jql_queries):
                        task_group.create_task(
                            get_issues_from_jira_jql(
                                project_key=project_key,
                                scope=scope,
                                batch_start_time=last_run_datetime,
                                http_session=http_session,
                                base_url=jira_entry.url,
                                url=f"{jira_entry.url}/rest/api/3/search/jql",
                                name=f"producer_{task_id}",
                                jql=jql_query,
                                total_records=total_records_estimate,
                                producer_count=num_producers,
                            ),
                            name=f"producer_{task_id}"
                        )

                    # Create consumer tasks
                    for consumer_id in range(CONSUMER_COUNT):
                        consumer_name = f"consumer_{project_key}_{FRUIT_NAMES[consumer_id]}"
                        task_group.create_task(
                            consume_issues(
                                consumer_id=consumer_id,
                                project_key=project_key,
                                http_session=http_session,
                                names=FRUIT_NAMES,
                                name=consumer_name,
                                num_producers=num_producers,
                                global_session_manager=global_session_manager,
                            ),
                            name=consumer_name
                        )

            # Wait for all queues to complete
            await _wait_for_queue_completion(q_container, project_key, my_logger)

            return True

        except *(TypeError, Exception) as exc_group:
            my_logger.error(f"Error in producer-consumer processing: {exc_group}")
            for exc in exc_group.exceptions:
                my_logger.error(f"Individual exception: {exc}")
        # return False


async def _wait_for_queue_completion(
        q_container: DynamicContainer,
        project_key: str,
        my_logger: Logger
):
    """Wait for all queues to complete processing."""
    my_logger.debug(f"Waiting for queue completion for {project_key}")

    # Log queue sizes before waiting
    queue_sizes = {
        name: queue.qsize()
        for name, queue in q_container.queue_selector().items()
    }
    my_logger.debug(f"Queue sizes before join: {queue_sizes}")

    active_tasks = [task for task in asyncio.all_tasks() if not task.done()]
    my_logger.debug(f"Active tasks: {len(active_tasks)}")

    for i, task in enumerate(active_tasks):
        my_logger.debug(f"Task {i}: {task}")
        # Get stack trace of hanging task
        task.print_stack()

    # Wait for all queues to complete
    await asyncio.gather(
        *(queue_name.join() for queue_name in q_container.queue_selector().values())
    )

    # Log final queue sizes
    final_sizes = {
        name: queue.qsize()
        for name, queue in q_container.queue_selector().items()
    }
    my_logger.debug(f"Final queue sizes: {final_sizes}")


async def _cleanup_orphaned_parent_references(pg_session, my_logger: Logger):
    """Clean up orphaned parent references in issues."""
    my_logger.debug("Cleaning up orphaned parent references")

    # Find parent_keys that don't have corresponding issue records
    issue_parent = aliased(Issue)
    orphaned_parents_subquery = (
        select(issue_parent.parent_key)
        .select_from(issue_parent)
        .outerjoin(Issue, issue_parent.parent_key == Issue.key)
        .filter(
            Issue.key.is_(None),
            issue_parent.parent_key.is_not(None)
        )
        .distinct()
    ).subquery()

    # Update issues to remove orphaned parent references
    update_stmt = (
        update(Issue)
        .where(Issue.parent_key.in_(select(orphaned_parents_subquery.c.parent_key)))
        .values(parent_key=null(), parent_id=null())
        .execution_options(synchronize_session='fetch')
    )

    result = await pg_session.execute(update_stmt)
    affected_rows = result.rowcount

    my_logger.info(f"Cleaned up {affected_rows} orphaned parent references")


async def _update_run_details(
        pg_session,
        process_start_marker: datetime,
        my_logger: Logger
):
    """Update run details after successful processing."""
    try:
        run_details = RunDetailsJira(topic="Issue", last_run=process_start_marker)
        await pg_session.merge(run_details)
        await pg_session.commit()
        my_logger.info("Run details updated successfully")

    except Exception as e:
        my_logger.error(f"Failed to update run details: {str(e)}")
        raise
# @inject
# async def process_jira_issues(
#         project_key: str, scope: str, initial_load: bool,
#         app_container: DynamicContainer = Provide[ApplicationContainer],
#         jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
#         q_container: DynamicContainer = Provide[QueueContainer],
#         my_logger: Logger = Provide[LoggerContainer.logger],
# ):
#     """
#     Process JIRA issues with improved error handling, transaction management,
#     and better separation of concerns.
#     """
#     asyncio.current_task().set_name(f"process_jira_issues_{project_key}")
#     try:
#         fruit_names = ["Apple", "Banana", "Cherry", "Dates", "Fig"]
#
#         # config_dict['url_seg'] = "/rest/api/3/myself"
#         endpoint = "/rest/api/3/myself"
#         url = f"{jira_entry.url}{endpoint}"
#         q_container.config.override({"schema_name": f"{project_key}"})
#
#         async with aiohttp.ClientSession(headers=jira_entry.custom_properties) as http_session:
#             response = await fetch_with_retries_get_old(http_session, url, my_logger=my_logger)
#             time_zone = ZoneInfo(response['timeZone'])
#             process_start_marker: datetime = datetime.now(tz=time_zone)
#
#             my_logger.info(f"project key passed is: {project_key}")
#
#             app_container.schema.override(project_key)
#             async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
#                 stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Issue")
#                 try:
#                     result = await pg_async_session.execute(stmt)
#                     local_datetime = result.scalar_one()
#                     my_logger.info(f"local datetime returned: {local_datetime}")
#                     _ = data_load.set("incremental")
#                 except sqlalchemy.exc.NoResultFound:
#                     # Create a datetime object with the specified date, time, and timezone
#                     local_datetime = datetime(year=2010, month=1, day=1, hour=0, minute=0, second=0,
#                                               tzinfo=time_zone)
#                     my_logger.debug(f"no rows found in RunDetailsJira. default time: {local_datetime}")
#                 except Exception as e:
#                     handle_exception(e)
#
#                 if scope == "project":
#                     if data_load.get() == "initial":
#                         jql = f"""
#                                         project={project_key.upper()}
#                                         and created > '{local_datetime.strftime('%Y-%m-%d %H:%M')}'
#                                         order by created asc
#                                     """
#                     else:
#                         jql = f"""
#                                 project = {project_key.upper()} and updated > '{local_datetime.strftime('%Y-%m-%d %H:%M')}'
#                                 order by updated asc
#                             """
#                 elif scope == "recon":
#                     jql = f"""
#                         project = {project_key.upper()} and issuetype = 'Initiative '
#                         """
#                 else:
#                     jql = None
#
#                 # Normalize whitespace by splitting and joining
#                 jql = ' '.join(jql.split())
#                 my_logger.debug(f"JQL = {jql}")
#                 jqls, total_records_estimate = await split_jql_by_count(jql)
#
#                 # Handle case where split_jql_by_count returns empty list or fails
#                 if not jqls:
#                     my_logger.warning("No JQLs returned from split_jql_by_count, using original JQL")
#                     jqls = [jql]
#                     total_records_estimate = -1
#
#                 try:
#                     # Store the number of producers for proper termination
#                     num_producers = len(jqls)
#                     my_logger.info(f"Starting {num_producers} producers and 5 consumers")
#
#                     async with asyncio.TaskGroup() as tg:
#                         for task_id in range(num_producers):
#                             tg.create_task(
#                                 get_issues_from_jira_jql(
#                                     project_key, scope,
#                                     local_datetime, http_session,
#                                     jira_entry.url, url=f"{jira_entry.url}/rest/api/3/search/jql",
#                                     name=f"producer_{task_id}", jql=jqls[task_id], total_records=total_records_estimate,
#                                     producer_count=num_producers,
#
#                                 )
#                             )
#
#
#                         for consumer_id in range(5):
#                             tg.create_task(
#                                 consume_issues(
#                                     consumer_id,
#                                     project_key,
#                                     pg_async_session,
#                                     # pg_session,
#                                     http_session,
#                                     names=fruit_names,
#                                     name=f"consumer_{project_key}_{fruit_names[consumer_id].lower()}",
#                                     num_producers=num_producers,  # Pass the number of producers
#                                 ),
#                                 name=f"consumer_{project_key}_{fruit_names[consumer_id].lower()}",
#                             )
#
#                         # active_queues = q_container.active_queues(project_key)
#                         my_logger.debug(f"{project_key} Waiting on queue completion")
#                         my_logger.debug(f"{project_key} QSize=")
#                         [
#                             my_logger.debug(
#                                 f"{project_key} -> {queue_name}: {queue_act.qsize()}")
#                             for queue_name, queue_act in q_container.queue_selector().items()
#                         ]
#
#                         # Ensure all queues are properly terminated
#                         # Add additional None signals if needed to ensure proper termination
#                         my_logger.debug(f"TaskGroup completed, ensuring proper queue termination")
#
#                         # Add a safety mechanism: if any producer failed to send None signals,
#                         # send additional None signals to ensure consumers don't hang
#                         current_queue_size = q_container.queue_selector()["queue_issues"].qsize()
#                         my_logger.debug(f"Current queue_issues size after TaskGroup: {current_queue_size}")
#
#                         # If there are still items in the queue or if we suspect missing None signals,
#                         # add additional None signals to ensure proper termination
#
#                         # for i in range(max(0, num_producers - current_queue_size)):
#                         #     my_logger.warning(f"Adding safety None signal {i+1} to queue_issues")
#                         #     await q_container.queue_selector()["queue_issues"].put(None)
#
#                         await asyncio.gather(
#                             *(
#                                 queue_name.join() for queue_name in q_container.queue_selector().values()
#                             )
#                         )
#                         my_logger.debug(f"{project_key} went past join")
#
#                         my_logger.debug(f"queues are all empty. QSize=")
#                         my_logger.debug(f"queue_changelog: {q_container.queue_selector()["queue_changelog"].qsize()}")
#                         my_logger.debug(f"queue_worklog: {q_container.queue_selector()["queue_worklog"].qsize()}")
#                         my_logger.debug(f"queue_comment: {q_container.queue_selector()["queue_comment"].qsize()}")
#                         my_logger.debug(
#                             f"queue_issue_links: {q_container.queue_selector()["queue_issue_links"].qsize()}")
#                         my_logger.debug(f"queue_issue: {q_container.queue_selector()["queue_issue"].qsize()}")
#                 except* TypeError as te:
#                     for err in te.exceptions:
#                         my_logger.error(err)
#                 except* Exception as ex:
#                     my_logger.error(ex)
#                     my_logger.debug(f"exception = {ex}")
#
#                 my_logger.debug(f"{project_key} Task group is done!!!")
#
#                 # Subquery to find parent_keys without corresponding keys
#                 issue_parent = aliased(Issue)
#                 find_parent_key = (
#                     select(issue_parent.parent_key)
#                     .select_from(issue_parent)
#                     .outerjoin(Issue, issue_parent.parent_key == Issue.key)
#                     .filter(Issue.key.is_(None), issue_parent.parent_key.is_not(None))
#                     .distinct()
#                 ).subquery()
#
#                 # Convert the subquery into a select statement explicitly
#                 find_parent_key_select = select(find_parent_key.c.parent_key)
#                 update_stmt = (
#                     update(Issue)
#                     .where(Issue.parent_key.in_(find_parent_key_select))
#                     .values(parent_key=null(), parent_id=null())
#                     .execution_options(synchronize_session='fetch')
#                 )
#                 my_logger.debug(f"going to execute update_stmt")
#                 await pg_async_session.execute(update_stmt)
#                 my_logger.debug(f"update_stmt done")
#
#                 # Set local_datetime to current time
#                 # local_datetime = datetime.now(tz=time_zone)
#
#                 # my_logger.debug(f"Commiting the transactions: {commit_transaction}")
#                 # if commit_transaction:
#                 #     pg_session.commit()
#                 # else:
#                 #     my_logger.warning(f"Transaction not committed as exception encountered")
#
#                 if commit_transaction:
#                     await pg_async_session.commit()
#                 else:
#                     my_logger.warning(f"Async Transaction not committed as exception encountered")
#
#                 if scope == "project":
#                     if commit_transaction:
#                         run_details = RunDetailsJira(topic="Issue", last_run=process_start_marker)
#                         await pg_async_session.merge(run_details)
#                         await pg_async_session.commit()
#                     else:
#                         my_logger.warning(
#                             f"{RunDetailsJira.__table__.name} Transaction not committed as exception encountered"
#                         )
#
#     except Exception as e:
#         handle_exception(e)
#     my_logger.info(f"process_jira_issues is done for {project_key}. ALL DONE!!!!")


# Define a type alias for clarity
QueuePair = tuple[Queue, str]
QueueList = list[QueuePair]


# @inject
# async def check_queue_status(
#         # queues: QueueList,
#         queue_issues: asyncio.Queue = Provide[QueueContainer.queue_issues],
#         queue_stats: asyncio.Queue = Provide[QueueContainer.queue_stats],
#         queue_issue: asyncio.Queue = Provide[QueueContainer.queue_issue],
#         queue_changelog: asyncio.Queue = Provide[QueueContainer.queue_changelog],
#         queue_worklog: asyncio.Queue = Provide[QueueContainer.queue_worklog],
#         queue_comment: asyncio.Queue = Provide[QueueContainer.queue_comment],
#         queue_issue_links: asyncio.Queue = Provide[QueueContainer.queue_issue_links],
#         my_logger: Logger = Provide[LoggerContainer.logger]
# ):
#     try:
#         table = Table(title="Queue Status", show_footer=False)
#         table.pad_edge = False
#         table_centered = Align.center(table)
#
#         # Initialize the table structure and rows
#         queues = [
#             (queue_issues, "queue_issues"), (queue_stats, "queue_stats"),
#             (queue_issue, "queue_issue"), (queue_changelog, "queue_changelog"),
#             (queue_worklog, "queue_worklog"), (queue_comment, "queue_comment"),
#             (queue_issue_links, "queue_issue_links"),
#         ]
#
#         def initialize_table():
#             """Creates the table structure and initializes rows once."""
#             table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
#             table.add_column("qsize", style="magenta", justify="right")
#
#             # Adding rows once
#             for queue_name, name in queues:
#                 table.add_row(name, str(queue_name.qsize()))
#
#         # Function to update only the 'qsize' column values
#         def update_table():
#             for index, (queue_name, name) in enumerate(queues):
#                 # Update the qsize column for each queue
#                 table.columns[1]._cells[index] = str(queue_name.qsize())
#
#         initialize_table()
#         with Live(table_centered, screen=False, transient=False, refresh_per_second=1) as live:
#             while True:
#                 update_table()
#                 live.update(table_centered)
#                 await asyncio.sleep(0.5)
#     except CancelledError:
#         my_logger.debug(f"function check_queue_status is cancelled")
#     except Exception as e:
#         handle_exception(e)


@inject
def create_db_extension(
        db_rw=Provide[DatabaseSessionManagerContainer.database_rw]
) -> str:
    return_value = "DB EXISTS"
    print(f"type : {type(db_rw)}")
    try:
        # with Database(schema='public').session() as pg_session:
        with db_rw.session() as pg_session:
            if not database_exists(pg_session.bind.url):
                create_database(pg_session.bind.url)
                return_value = f"DB CREATED"

            # Create extensions
            for value in [
                "citext", "ltree", "intarray", "hstore", "btree_gist", "pg_trgm"
            ]:
                pg_session.execute(
                    text(f"CREATE EXTENSION IF NOT EXISTS {value} WITH SCHEMA pg_catalog;")
                )
                print(f"extension {value} created")
    except Exception as e:
        return_value = "EXCEPTION"
        handle_exception(e)
    finally:
        return return_value


def generate_password(length: int = 12):
    characters = string.ascii_letters + string.digits + string.punctuation
    return ''.join(random.choice(characters) for _ in range(length))


def md5_hash(password, role_name):
    raw = (password + role_name).encode()
    return "md5" + hashlib.md5(raw).hexdigest()


@inject
def create_schema_tables_ddl(
        schema_name: str,
        db_rw=Provide[DatabaseSessionManagerContainer.database_rw],
        db_ro=Provide[DatabaseSessionManagerContainer.database_ro],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:
    function_status = False

    try:
        my_logger.debug(f"opening the session")
        with db_rw.session() as pg_session:
            with pg_session.begin():
                # with Database(schema=schema_name).session() as pg_session:
                # Apply the function and trigger to each schema
                engine = pg_session.bind
                inspector = inspect(engine)

                # if not pg_session.bind.dialect.has_schema(pg_session.bind, schema_name):
                if schema_name not in inspector.get_schema_names():
                    pg_session.execute(CreateSchema(schema_name, if_not_exists=True))

                # Create users if they don't exist
                rw_password = generate_password()
                ro_password = generate_password()

                rw_password_md5 = md5_hash(rw_password, f"{schema_name}_rw")
                ro_password_md5 = md5_hash(ro_password, f"{schema_name}_ro")

                # Create roles safely
                # if using md5, then instead of rw_password pass rw_password_md5.
                # for clarity :rw_password can be relabelled as :rw_password_md5

                # Create RW role
                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        role_name TEXT := :rw_role_name;
                        role_password TEXT := :rw_password;
                    BEGIN 
                        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = role_name) THEN 
                            EXECUTE 'CREATE ROLE ' || quote_ident(role_name) || ' LOGIN PASSWORD ' || quote_literal(role_password);  
                        END IF; 
                    END $$;
                """), {"rw_role_name": f"{schema_name}_rw", "rw_password": rw_password}
                )

                # Create RO role
                pg_session.execute(text("""
                    DO $$ 
                    DECLARE
                        role_name TEXT := :ro_role_name;
                        role_password TEXT := :ro_password;
                    BEGIN 
                        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = role_name) THEN 
                            EXECUTE 'CREATE ROLE ' || quote_ident(role_name) || ' LOGIN PASSWORD ' || quote_literal(role_password); 
                        END IF;  
                    END $$;
                """), {"ro_role_name": f"{schema_name}_ro", "ro_password": ro_password})

                # Set default privileges for tables, functions, and sequences
                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' GRANT ALL PRIVILEGES ON TABLES TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        ro_role_name_val TEXT := :ro_role_name;
                    BEGIN 
                        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' GRANT SELECT ON TABLES TO ' || quote_ident(ro_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "ro_role_name": f"{schema_name}_ro"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' GRANT EXECUTE ON FUNCTIONS TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' GRANT USAGE, SELECT ON SEQUENCES TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                # Grant permissions on schema and existing objects
                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'GRANT ALL PRIVILEGES ON SCHEMA ' || quote_ident(schema_name_val) || 
                                ' TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        ro_role_name_val TEXT := :ro_role_name;
                    BEGIN 
                        EXECUTE 'GRANT SELECT ON ALL TABLES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' TO ' || quote_ident(ro_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "ro_role_name": f"{schema_name}_ro"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        schema_name_val TEXT := :schema_name;
                        ro_role_name_val TEXT := :ro_role_name;
                    BEGIN 
                        EXECUTE 'GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA ' || quote_ident(schema_name_val) || 
                                ' TO ' || quote_ident(ro_role_name_val);
                    END $$;
                    """), {"schema_name": schema_name, "ro_role_name": f"{schema_name}_ro"}
                )

                # Grant select permission on public
                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        rw_role_name_val TEXT := :rw_role_name;
                    BEGIN 
                        EXECUTE 'GRANT SELECT ON ALL TABLES IN SCHEMA public TO ' || quote_ident(rw_role_name_val);
                    END $$;
                    """), {"rw_role_name": f"{schema_name}_rw"}
                )

                pg_session.execute(
                    text("""
                    DO $$ 
                    DECLARE
                        ro_role_name_val TEXT := :ro_role_name;
                    BEGIN 
                        EXECUTE 'GRANT SELECT ON ALL TABLES IN SCHEMA public TO ' || quote_ident(ro_role_name_val);
                    END $$;
                    """), {"ro_role_name": f"{schema_name}_ro"}
                )
            pg_session.commit()

            # Access the engine via session.bind
            engine_rw = pg_session.bind.engine
            with db_ro.session() as pg_ro:
                engine_ro = pg_ro.bind.engine

            my_logger.debug(f"adding entries to keepass")
            # Add entries to KeePass
            kp = PyKeePass(filename=f"{os.getenv('AIRFLOW_HOME')}/Database.kdbx",
                           keyfile=f"{os.getenv('AIRFLOW_HOME')}/Database.key")

            group = kp.find_groups(name='DB', first=True)

            if not kp.find_entries(title=f"{schema_name}_rw", first=True):
                kp.add_entry(
                    group, title=f"{schema_name}_rw", username=f"{schema_name}_rw", password=f"{rw_password}"
                )
                entry = kp.find_entries(title=f"{schema_name}_rw", first=True)
                for key, value in [
                    ('DB_NAME', engine_rw.url.database),
                    ('DB_SERVER_NAME', engine_rw.url.host),
                    ('DB_SERVER_RO_PORT', str(engine_ro.url.port)),
                    ('DB_SERVER_RW_PORT', str(engine_rw.url.port)),
                ]:
                    entry.set_custom_property(key=key, value=value)
                my_logger.info(f"password {rw_password} set for username={schema_name}_rw")
            else:
                my_logger.info(f"entry with {schema_name}_rw exits")

            if not kp.find_entries(title=f"{schema_name}_ro", first=True):
                kp.add_entry(
                    group, title=f"{schema_name}_ro", username=f"{schema_name}_ro", password=f"{ro_password}"
                )
                entry = kp.find_entries(title=f"{schema_name}_ro", first=True)
                for key, value in [
                    ('DB_NAME', engine_rw.url.database),
                    ('DB_SERVER_NAME', engine_rw.url.host),
                    ('DB_SERVER_RO_PORT', str(engine_ro.url.port)),
                    ('DB_SERVER_RW_PORT', str(engine_rw.url.port)),
                ]:
                    entry.set_custom_property(key=key, value=value)
                my_logger.info(f"password {ro_password} set for username={schema_name}_ro")
            else:
                my_logger.info(f"entry with {schema_name}_ro exits")

            kp.save()

            # Define the DDL for creating the function
            function_issue_tsv_vector = DDL(
                """
                DO 
                $do$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_proc
                        WHERE proname = 'update_tsv_combined'
                    ) THEN
                        CREATE OR REPLACE FUNCTION update_tsv_combined() RETURNS trigger AS $$
                        BEGIN
                            NEW.tscv_summary_description :=
                                setweight(to_tsvector('english', coalesce(NEW.summary, '')), 'A') ||
                                setweight(to_tsvector('english', coalesce(NEW.description_markdown, '')), 'B');
                            RETURN NEW;
                        END
                        $$ LANGUAGE plpgsql;
                    END IF;
                END $do$;
                """
            )
            # Listen for the 'after_create' event to create the function
            event.listen(
                Issue.__table__,
                'after_create',
                function_issue_tsv_vector.execute_if(dialect='postgresql')
            )

            def create_trigger_ddl(schema_name):
                """Create a safe DDL statement for the trigger with proper schema name handling"""
                return DDL(
                    """
                    DO $do$
                    DECLARE
                        schema_name_val TEXT := %(schema_name)s;
                        table_oid OID;
                    BEGIN
                        -- Get the table OID safely
                        SELECT c.oid INTO table_oid
                        FROM pg_class c
                        JOIN pg_namespace n ON c.relnamespace = n.oid
                        WHERE c.relname = 'issue' AND n.nspname = schema_name_val;

                        IF NOT EXISTS (
                            SELECT 1 FROM pg_trigger
                            WHERE tgname = 'tsv_combined_update'
                            AND tgrelid = table_oid
                        ) THEN
                            EXECUTE 'CREATE TRIGGER tsv_combined_update ' ||
                                    'BEFORE INSERT OR UPDATE ON ' || quote_ident(schema_name_val) || '.issue ' ||
                                    'FOR EACH ROW EXECUTE FUNCTION update_tsv_combined()';
                        END IF;
                    END $do$;
                    """,
                    {"schema_name": schema_name}
                )

            trigger_issue_tsv_summary_description = create_trigger_ddl(schema_name)

            # Listen for the 'after_create' event to create the trigger, passing the schema dynamically
            event.listen(
                Issue.__table__,
                'after_create',
                trigger_issue_tsv_summary_description.execute_if(dialect=("postgresql",))
            )

            pg_session.bind.update_execution_options(schema_translate_map={None: schema_name})
            Base.metadata.create_all(bind=pg_session.bind, checkfirst=True)
            pg_session.commit()
            function_status = True
    except Exception as e:
        handle_exception(e)
    finally:
        return 'TRUE' if function_status else 'FALSE'


async def add_comment(
        issue_key: str,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details]
):
    # config_dict = get_env_variables()
    base_url = jira_entry.url
    endpoint = f"/rest/api/3/issue/{issue_key}"
    # config_dict['url_seg'] = f"/rest/api/3/issue/{issue_key}"
    payload = {'fields': ['issuekey', 'assignee', 'status', 'reporter']}

    url_issue = f"{base_url}{endpoint}"
    url_issue_comment = f"{base_url}{endpoint}/comment"
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(headers=jira_entry.custom_properties, timeout=timeout) as http_session:
        response = await fetch_with_retries_get_new(http_session, url_issue, params=payload)
        response = response['result']
        issue_id = response['id']
        assignee = response['fields']['assignee']
        reporter = response['fields']['reporter']
        status = response['fields']['status']

        if assignee:
            print(assignee.get('accountId', None), assignee.get('emailAddress', None),
                  assignee.get('displayName', None))
        if status:
            print(status.get('id'), status.get('name'), status.get('statusCategory').get('name'), )
        print(reporter.get('accountId'), reporter.get('emailAddress'), reporter.get('displayName'))

        params_comments = {
            "startAt": 0,
            "maxResults": 5000
        }

        response = await fetch_with_retries_get_new(http_session, url_issue_comment, params=params_comments)
        rprint(response)

        dictionary = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "mention",
                                "attrs": {
                                    "id": reporter.get('accountId'),
                                    "text": reporter.get('displayName'),
                                    "accessLevel": ""
                                }
                            },
                            {
                                "type": "text",
                                "text": "This is test comment 11/8/2024",
                            },
                            {
                                "type": "hardBreak"
                            },
                        ]
                    }
                ]
            }
        }

        response = await fetch_with_retries_post(
            http_session, url=url_issue_comment,
            json_payload=dictionary
        )
        print(response)


# Section for defining ContextVar
commit_transaction: bool = True
lock = asyncio.Lock()
# Define a global or class-level lock
issue_links_lock = asyncio.Lock()
issue_worklog_lock = asyncio.Lock()
issue_changelog_lock = asyncio.Lock()
issue_comment_lock = asyncio.Lock()
issue_lock = asyncio.Lock()
issue_initiative_attribute_lock = asyncio.Lock()

data_load: ContextVar[str] = ContextVar("data_load", default="initial")


# Task done callback
@inject
def helper_done_callback(task, my_logger: Logger = Provide[LoggerContainer.logger]):
    if task.exception():
        try:
            task.result()
        except CancelledError as e:
            my_logger.warning(f"{task} is cancelled")
        except Exception as e:
            my_logger.exception(f'Task {task} failed with exception {e}')
            raise e


def create_task_helper(coroutine, name: str | None = None, context_param=None):
    task = asyncio.create_task(coroutine, name=name, context=context_param)
    task.add_done_callback(helper_done_callback)
    return task


def create_task_group_helper(coroutine, task_group, name: str | None = None, context_param=None):
    task = task_group.create_task(coroutine, name=name, context=context_param)
    task.add_done_callback(helper_done_callback)

    return task


# Report duration of all current tasks
# Assumes task names are unique
@inject
async def monitor(
        task_limit_sec, exclude_names=(),
        monitor_panel=None,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    # Record of all task names and their start times
    task_dict = dict()

    # Create a table to display monitored task info
    def reset_table():
        # terminal_width = monitor_panel.size.width
        terminal_width = 130
        # column_count = 6 if terminal_width >= 120 else 4 if terminal_width >= 80 else 2
        task_column_count = 2

        monitor_tb = Table(
            title="Long-Running Tasks",
            show_header=True,
            header_style="bold magenta"
        )
        monitor_tb.add_column("Task Name", style="cyan")
        monitor_tb.add_column("Duration (s)", style="magenta")
        if task_column_count > 4:
            monitor_tb.add_column("Start Time", style="green")
            monitor_tb.add_column("Status", style="yellow")
        if task_column_count > 2:
            monitor_tb.add_column("Priority", style="blue")
            monitor_tb.add_column("Description", style="white")

        return monitor_tb, task_column_count

    while True:
        # Clear table for updated info
        monitor_table, column_count = reset_table()

        # Get all tasks except those with specified names
        tasks = [t for t in asyncio.all_tasks() if t.get_name() not in exclude_names]

        for task in tasks:
            # Get task name
            name = task.get_name()
            # Check if not previously known
            if name not in task_dict:
                # Add start time (first time seen)
                task_dict[name] = time.monotonic()
                continue
            # Compute duration for current task
            duration = time.monotonic() - task_dict[name]
            # Check if not too long
            if duration > task_limit_sec:
                # Add task info to the table
                monitor_table.add_row(
                    name, f"{duration:.3f}",
                    # "N/A" if column_count <= 4 else time.strftime('%H:%M:%S', time.gmtime(task_dict[name])),
                    # "Running" if column_count > 4 else "",
                    # "High" if column_count > 2 else "",
                    # "Monitored Task" if column_count > 2 else ""
                )
                # Report task that has been alive too long
                # my_logger.debug(f'{name} alive for too long: {duration:.3f} seconds')

        # Update monitor panel if provided
        if monitor_panel:
            monitor_panel.update(Panel(monitor_table, title="Task Monitor", border_style="red"))

        # Check every ten seconds
        await asyncio.sleep(1)


def create_layout():
    layout = Layout(name="root")
    width, height = shutil.get_terminal_size()

    if width > 120 and height > 30:
        # Large screens with ample space

        layout.split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3)
        )
        layout["left"].split_column(
            Layout(name="left-first", size=height // 6),
            Layout(name="left-second", size=height // 3),
            Layout(name="left-third", ratio=1),
        )
        layout["right"].split_column(
            Layout(name="right-top", size=height // 2),
            Layout(name="right-bottom", ratio=1),
        )
    elif 80 < width <= 120 or 20 < height <= 30:  # Medium-sized screens
        layout.split_column(
            Layout(name="main", ratio=1),
        )
        layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3)
        )
        layout["left"].split_column(
            Layout(name="left-first", size=height // 8),
            Layout(name="left-second", size=height // 4),
            Layout(name="left-third", ratio=1),
        )
        layout["right"].split_column(
            Layout(name="right-top", size=height // 3),
            Layout(name="right-bottom", ratio=1),
        )
    else:  # Small screens
        layout.split_column(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3),
        )
        layout["left"].split_column(
            Layout(name="left-first", size=3),
            Layout(name="left-second", ratio=4),
            Layout(name="left-third", size=3),
        )
        layout["right"].split_column(
            Layout(name="right-top", ratio=3),
            Layout(name="right-bottom", ratio=2),
        )

    return layout


@dataclass
class TaskInfo:
    function: Callable
    args: List[Any]
    kwargs: dict
    description: str = ""

    def __post_init__(self):
        if not self.description:
            self.description = self.function.__name__


@dataclass
class ProcessingStats:
    total_records_processed: int = 0
    start_time: float = 0
    last_update_time: float = 0
    processing_rates: List[float] = None

    def __post_init__(self):
        if self.processing_rates is None:
            self.processing_rates = []
        if self.start_time == 0:
            self.start_time = time.time()
        if self.last_update_time == 0:
            self.last_update_time = time.time()


class ManagedProgress(Progress):
    """Custom Progress class that automatically manages completed tasks"""

    def __init__(self, *args, max_completed_visible: int = 4, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_completed_visible = max_completed_visible
        self.completed_tasks = []  # Track completed tasks in order
        self.hidden_tasks = set()  # Track hidden task IDs

    def mark_completed(self, task_id: TaskID, return_value: str = "Completed"):
        """Mark a task as completed and manage visibility"""
        # Update the task
        self.update(task_id, advance=100, return_value=return_value)
        self.stop_task(task_id)

        # Track as completed
        if task_id not in self.completed_tasks:
            self.completed_tasks.append(task_id)

        # Hide oldest completed tasks if needed
        if len(self.completed_tasks) > self.max_completed_visible:
            tasks_to_hide = self.completed_tasks[:-self.max_completed_visible]

            for old_task_id in tasks_to_hide:
                if old_task_id not in self.hidden_tasks:
                    self._hide_task(old_task_id)
                    self.hidden_tasks.add(old_task_id)

            # Remove hidden tasks from completed list
            self.completed_tasks = [t for t in self.completed_tasks if t not in self.hidden_tasks]

    def _hide_task(self, task_id: TaskID):
        """Hide a completed task"""
        try:
            # Method 1: Try to remove the task entirely
            if hasattr(self, '_tasks') and task_id in self._tasks:
                del self._tasks[task_id]
        except:
            try:
                # Method 2: Make it invisible by setting description to empty
                self.update(task_id, description="", return_value="")
                # Reset progress to 0 to minimize space
                task = self._tasks.get(task_id)
                if task:
                    task.total = 0
                    task.completed = 0
            except:
                pass

    def get_visible_tasks(self):
        """Get only visible (non-hidden) tasks"""
        if hasattr(self, '_tasks'):
            return {tid: task for tid, task in self._tasks.items() if tid not in self.hidden_tasks}
        return {}

    def cleanup_hidden_tasks(self):
        """Remove all hidden tasks"""
        for task_id in list(self.hidden_tasks):
            try:
                if hasattr(self, '_tasks') and task_id in self._tasks:
                    del self._tasks[task_id]
            except:
                pass
        self.hidden_tasks.clear()

class RichDisplayManager:
    """Manages Rich UI components and ensures single Live instance"""

    def __init__(self):
        self.console = Console()
        self.live: Optional[Live] = None
        self.layout: Optional[Layout] = None
        self._is_active = False

    def create_layout(self) -> Layout:
        """Create the main layout structure"""
        layout = Layout()
        layout.split_row(
            Layout(name="left"),
            Layout(name="right", ratio=2)
        )

        layout["left"].split_column(
            Layout(name="overall", ratio=1),
            Layout(name="queues", ratio=2),
            Layout(name="stats_progress", ratio=1),
            Layout(name="stats_table", ratio=2)
        )

        layout["right"].split_column(
            Layout(name="tasks", ratio=3),
            Layout(name="monitor", ratio=2),
        )
        self.layout = layout
        return layout

    @asynccontextmanager
    async def managed_live(self, layout):
        """Context manager for Rich Live to ensure single instance"""
        if self._is_active:
            raise RuntimeError("Rich Live is already active")

        self._is_active = True
        self.live = Live(layout, auto_refresh=True, screen=False, transient=False)

        try:
            with self.live:
                yield self.live
        finally:
            self._is_active = False
            self.live = None

    def update_layout_section(self, section: str, content):
        """Update a specific section of the layout"""
        if self.layout and section in self.layout:
            self.layout[section].update(content)


class ProgressManager:
    """Manages all progress bars and statistics"""

    def __init__(self):
        self.stats_task_id = None
        self.overall_task_id = None
        self.overall_progress = None
        self.job_progress = None
        self.stats_progress = None
        self.stats_table = None
        self.queue_status_table = None
        self.processing_stats = ProcessingStats()
        self.completed_tasks = []  # Track completed task IDs in order
        self.max_visible_completed = 4  # Maximum number of completed tasks to show

    def initialize_progress_bars(self, total_tasks: int):
        """Initialize all progress bar components"""
        self.overall_progress = Progress(
            "{task.description}",
            MofNCompleteColumn(separator="/")
        )

        # Use the custom ManagedProgress for job progress
        self.job_progress = ManagedProgress(
            "{task.description}",
            TextColumn("[bold]Params:[/bold] {task.fields[parameters]}", justify="left"),
            SpinnerColumn(),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            " ",
            TimeElapsedColumn(),
            TextColumn("{task.fields[return_value]}", justify="left"),
            max_completed_visible = 3
        )

        self.stats_progress = Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            "[progress.percentage]{task.percentage:>3.0f}%",
            "•",
            TextColumn("Processed: {task.completed} / {task.total}"),
            TimeElapsedColumn(),
        )

        # Initialize overall task
        self.overall_task_id = self.overall_progress.add_task("Overall Task Progress", total=total_tasks)

        # Initialize stats task
        self.stats_task_id = self.stats_progress.add_task(
            description="Processing Records", total=None, start=False
        )

    def initialize_stats_table(self):
        """Initialize the statistics table"""
        self.stats_table = Table(
            show_footer=False, show_header=True, title="Processing Statistics"
        )
        self.stats_table.add_column("Metric", justify="left", style="cyan", no_wrap=True)
        self.stats_table.add_column("Value", justify="right", style="green")
        self.stats_table.add_column("Unit", justify="left", style="dim")

        # Initialize with default values
        stats_data = [
            ("Records Processed", "0", "records"),
            ("Total Time", "0.0", "seconds"),
            ("Current TPS", "0.0", "records/sec"),
            ("Average TPS", "0.0", "records/sec"),
            ("Completion", "0.0", "%")
        ]

        for metric, value, unit in stats_data:
            self.stats_table.add_row(metric, value, unit)

    def initialize_queue_table(self, schema: str, q_container):
        """Initialize the queue status table"""
        self.queue_status_table = Table(
            show_footer=False, show_header=False
        )
        self.queue_status_table.pad_edge = False
        self.queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
        self.queue_status_table.add_column("qsize", style="magenta", justify="right")
        self.queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
        self.queue_status_table.add_column("qsize", style="magenta", justify="right")

        # Initialize table with current queue data
        q_container.config.override({"schema_name": schema})
        queue_items = list(q_container.queue_selector().items())

        for i in range(0, len(queue_items), 2):
            q1_name, q1_queue = queue_items[i]
            q1_size = str(q1_queue.qsize())

            if i + 1 < len(queue_items):
                q2_name, q2_queue = queue_items[i + 1]
                q2_size = str(q2_queue.qsize())
            else:
                q2_name, q2_size = "", ""

            self.queue_status_table.add_row(q1_name, q1_size, q2_name, q2_size)

    def add_job_task(self, task_info: TaskInfo) -> int:
        """Add a job task to the progress tracker"""
        argc = len(task_info.args) + len(task_info.kwargs)
        argv = ""

        if task_info.args:
            argv = f"{', '.join(map(str, task_info.args))} "

        if task_info.kwargs:
            kwargs_str = " ".join(f"{k}={v}" for k, v in task_info.kwargs.items())
            argv += kwargs_str

        task_description = f"[cyan]{task_info.function.__name__}"

        return self.job_progress.add_task(
            task_description,
            total=100,
            parameters=argv,
            return_value="Pending...",
            start=False
        )

    def mark_task_completed(self, task_id: int, result: str = "Completed"):
        """Mark a task as completed using the managed progress functionality"""
        if hasattr(self.job_progress, 'mark_completed'):
            # Use the custom ManagedProgress method
            self.job_progress.mark_completed(task_id, result)
        else:
            # Fallback to manual management
            if task_id not in self.completed_tasks:
                self.completed_tasks.append(task_id)

            self.job_progress.update(task_id, advance=100, return_value=result)
            self.job_progress.stop_task(task_id)

            if len(self.completed_tasks) > self.max_visible_completed:
                tasks_to_remove = self.completed_tasks[:-self.max_visible_completed]
                for old_task_id in tasks_to_remove:
                    self._hide_completed_task(old_task_id)
                self.completed_tasks = self.completed_tasks[-self.max_visible_completed:]

    def _hide_completed_task(self, task_id: int):
        """Hide a completed task by updating its description (fallback method)"""
        try:
            # Update the task to show it's been archived
            self.job_progress.update(
                task_id,
                description="[dim]Archived",
                return_value="[dim]Task completed and archived"
            )
            # Reset the task to 0 to effectively hide it
            self.job_progress.reset(task_id, total=0)
        except Exception:
            # If all else fails, just ignore the error
            pass

    def get_active_tasks_count(self) -> int:
        """Get count of currently active (non-completed) tasks"""
        total_tasks = len(self.job_progress.tasks) if self.job_progress else 0
        return total_tasks - len(self.completed_tasks)

    def set_max_visible_completed(self, max_count: int):
        """Set the maximum number of completed tasks to keep visible"""
        self.max_visible_completed = max_count
        if hasattr(self.job_progress, 'max_completed_visible'):
            self.job_progress.max_completed_visible = max_count

    def get_completed_tasks_count(self) -> int:
        """Get the number of completed tasks"""
        if hasattr(self.job_progress, 'completed_tasks'):
            return len(self.job_progress.completed_tasks)
        return len(self.completed_tasks)

    def cleanup_all_completed_tasks(self, stats_result: dict = None):
        """Remove all completed tasks from display with improved error handling"""
        try:
            if hasattr(self.job_progress, 'cleanup_hidden_tasks'):
                self.job_progress.cleanup_hidden_tasks()
            else:
                # Create a copy to avoid modification during iteration
                completed_tasks_copy = self.completed_tasks.copy()
                for task_id in completed_tasks_copy:
                    try:
                        self.job_progress.remove_task(task_id)
                        self.completed_tasks.remove(task_id)  # Use discard to avoid KeyError
                    except Exception as e:
                        # Log the error but continue cleanup
                        if hasattr(self, 'my_logger'):
                            self.my_logger.debug(f"Failed to remove task {task_id}: {e}")
                        self._hide_completed_task(task_id)
                        self.completed_tasks.remove(task_id)

            # Update processing statistics if provided
            if stats_result and stats_result.get('record_count', 0) > 0:
                records_processed = stats_result['record_count']
                self.processing_stats.total_records_processed += records_processed

                current_time = time.time()
                elapsed_time = current_time - self.processing_stats.start_time
                time_since_last_update = current_time - self.processing_stats.last_update_time

        except Exception as e:
            # Log cleanup errors but don't raise them
            if hasattr(self, 'my_logger'):
                self.my_logger.error(f"Error during task cleanup: {e}")

    def force_cleanup_all_tasks(self):
        """Force cleanup of all tasks - use for final cleanup"""
        try:
            if hasattr(self.job_progress, 'cleanup_hidden_tasks'):
                self.job_progress.cleanup_hidden_tasks()

            # Clear all completed tasks
            self.completed_tasks.clear()

            # Try to remove all tasks from progress display
            if hasattr(self.job_progress, 'task_ids'):
                for task_id in list(self.job_progress.task_ids):
                    try:
                        self.job_progress.remove_task(task_id)
                    except Exception:
                        pass  # Ignore errors during force cleanup

        except Exception as e:
            if hasattr(self, 'my_logger'):
                self.my_logger.error(f"Error during force cleanup: {e}")

        # # Calculate TPS metrics
        # current_tps = records_processed / time_since_last_update if time_since_last_update > 0 else 0
        # self.processing_stats.processing_rates.append(current_tps)
        #
        # # Keep only last 10 TPS values
        # if len(self.processing_stats.processing_rates) > 10:
        #     self.processing_stats.processing_rates.pop(0)
        #
        # avg_tps = sum(self.processing_stats.processing_rates) / len(self.processing_stats.processing_rates)
        # completion_pct = (self.processing_stats.total_records_processed /
        #                   max(stats_result['total'], self.processing_stats.total_records_processed) * 100) if \
        # stats_result['total'] > 0 else 0
        #
        # # Update stats table
        # self.stats_table.columns[1]._cells[0] = f"{self.processing_stats.total_records_processed:,}"
        # self.stats_table.columns[1]._cells[1] = f"{elapsed_time:.1f}"
        # self.stats_table.columns[1]._cells[2] = f"{current_tps:.1f}"
        # self.stats_table.columns[1]._cells[3] = f"{avg_tps:.1f}"
        # self.stats_table.columns[1]._cells[4] = f"{completion_pct:.1f}"

    def update_stats(self, stats_result: dict):
        """Update processing statistics"""
        records_processed = stats_result['record_count']
        self.processing_stats.total_records_processed += records_processed

        current_time = time.time()
        elapsed_time = current_time - self.processing_stats.start_time
        time_since_last_update = current_time - self.processing_stats.last_update_time

        # Calculate TPS metrics
        current_tps = records_processed / time_since_last_update if time_since_last_update > 0 else 0
        self.processing_stats.processing_rates.append(current_tps)

        # Keep only last 10 TPS values
        if len(self.processing_stats.processing_rates) > 10:
            self.processing_stats.processing_rates.pop(0)

        avg_tps = sum(self.processing_stats.processing_rates) / len(self.processing_stats.processing_rates)
        completion_pct = (self.processing_stats.total_records_processed /
                          max(stats_result['total'], self.processing_stats.total_records_processed) * 100) if \
        stats_result['total'] > 0 else 0

        # Update stats table
        self.stats_table.columns[1]._cells[0] = f"{self.processing_stats.total_records_processed:,}"
        self.stats_table.columns[1]._cells[1] = f"{elapsed_time:.1f}"
        self.stats_table.columns[1]._cells[2] = f"{current_tps:.1f}"
        self.stats_table.columns[1]._cells[3] = f"{avg_tps:.1f}"
        self.stats_table.columns[1]._cells[4] = f"{completion_pct:.1f}"

        self.processing_stats.last_update_time = current_time

        # Update progress bar
        self.stats_progress.update(self.stats_task_id, advance=records_processed)
        self.stats_progress.update(self.stats_task_id,
                                   total=max(stats_result['total'], self.processing_stats.total_records_processed))


class TaskExecutor:
    """Handles task execution with monitoring"""

    def __init__(self, q_container, my_logger, progress_manager: ProgressManager):
        self.q_container = q_container
        self.my_logger = my_logger
        self.progress_manager = progress_manager
        self.background_tasks = set()

    async def monitor_stats(self, schema: str):
        """Monitor stats queue with improved error handling"""
        none_count = 0
        self.q_container.config.override({"schema_name": schema})

        cleanup_counter = 0
        try:
            while True:
                stats_result = {}
                try:
                    stats_result = await self.q_container.queue_selector()["queue_stats"].get()

                    if stats_result.get('isLast', True):
                        none_count += 1

                    if none_count >= stats_result.get('producer_count', 10):
                        self.my_logger.info(f"Stats monitoring completed. Final count: {none_count}")
                        if self.q_container.queue_selector()["queue_stats"].qsize() == 0:
                            stats_result = {}
                            break

                    self.progress_manager.update_stats(stats_result)

                    # Increment cleanup counter
                    cleanup_counter += 1

                except Exception as e:
                    self.my_logger.error(f"Error in stats monitoring: {e}")

                finally:
                    self.q_container.queue_selector()["queue_stats"].task_done()

                    # Perform cleanup on every stats update
                    self.progress_manager.cleanup_all_completed_tasks(stats_result)

                    # Additional thorough cleanup every 10 iterations
                    if cleanup_counter % 10 == 0 and stats_result.get('producer_count', 10) == 10:
                        self.progress_manager.cleanup_all_completed_tasks()
                        self.my_logger.debug(f"Thorough cleanup performed at stats iteration {cleanup_counter}")

        except Exception as e:
            self.my_logger.error(f"Stats monitoring failed: {e}")

    async def update_queue_counts(self, schema: str):
        """Update queue status table periodically"""
        self.q_container.config.override({"schema_name": schema})
        queue_items = list(self.q_container.queue_selector().items())

        try:
            while True:
                for i in range(0, len(queue_items), 2):
                    q1_name, q1_queue = queue_items[i]
                    q1_size = str(q1_queue.qsize())

                    if i + 1 < len(queue_items):
                        q2_name, q2_queue = queue_items[i + 1]
                        q2_size = str(q2_queue.qsize())
                    else:
                        q2_name, q2_size = "", ""

                    # Update table cells
                    row_index = i // 2
                    if row_index < len(self.progress_manager.queue_status_table.columns[0]._cells):
                        self.progress_manager.queue_status_table.columns[0]._cells[row_index] = q1_name
                        self.progress_manager.queue_status_table.columns[1]._cells[row_index] = q1_size
                        self.progress_manager.queue_status_table.columns[2]._cells[row_index] = q2_name
                        self.progress_manager.queue_status_table.columns[3]._cells[row_index] = q2_size

                await asyncio.sleep(1)

        except asyncio.CancelledError:
            self.my_logger.info("Queue monitoring cancelled")
        except Exception as e:
            self.my_logger.error(f"Queue monitoring error: {e}")

    async def execute_task(self, task_info: TaskInfo, task_id: int = None) -> Any:
        """Execute a single task with progress tracking"""
        try:
            self.my_logger.debug(f"Processing {task_info.function.__name__} with {task_info.args}")

            if task_id is not None:
                self.progress_manager.job_progress.start_task(task_id)

            result = None

            # Special handling for process_jira_issues
            if task_info.function.__name__ == "process_jira_issues":
                schema = task_info.args[0] if task_info.args else ""

                # Start background monitoring tasks
                queue_task = asyncio.create_task(
                    self.update_queue_counts(schema),
                    name=f"queue_monitor_{schema}"
                )
                stats_task = asyncio.create_task(
                    self.monitor_stats(schema),
                    name=f"monitor_stats_{schema}"
                )

                self.background_tasks.update([queue_task, stats_task])

                try:
                    self.my_logger.info(f"Starting {task_info.function.__name__}")
                    result = await task_info.function(*task_info.args, **task_info.kwargs)
                    self.my_logger.info(f"{task_info.function.__name__} completed")

                finally:
                    # Clean up background tasks
                    for task in [queue_task, stats_task]:
                        if not task.done():
                            task.cancel()
                            try:
                                await task
                            except asyncio.CancelledError:
                                pass

                    self.background_tasks.discard(queue_task)
                    self.background_tasks.discard(stats_task)
            else:
                # Regular task execution
                if asyncio.iscoroutinefunction(task_info.function):
                    result = await task_info.function(*task_info.args, **task_info.kwargs)
                else:
                    result = task_info.function(*task_info.args, **task_info.kwargs)

            if task_id is not None:
                self.progress_manager.job_progress.update(
                    task_id, advance=100, return_value=str(result)
                )
                self.progress_manager.job_progress.stop_task(task_id)

            self.my_logger.debug(f"Completed {task_info.function.__name__}")
            return result

        except Exception as e:
            self.my_logger.error(f"Error executing {task_info.function.__name__}: {e}")
            if task_id is not None:
                self.progress_manager.job_progress.update(
                    task_id, advance=100, return_value=f"Error: {str(e)[:50]}"
                )
            raise

    async def cleanup(self):
        """Clean up background tasks"""
        for task in list(self.background_tasks):
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        self.background_tasks.clear()


@inject
async def process_task(
        display_rich: bool = True,
        projects: tuple = (),
        q_container: DynamicContainer = Provide[QueueContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    """Refactored process_task with improved Rich Live management"""

    # Set up current task name
    my_current_task = asyncio.current_task()
    my_current_task.set_name("process_task")

    # Get project list
    # schemas = q_container.schemas()
    project_list = projects

    # Define tasks using TaskInfo dataclass
    task_definitions = [
        # TaskInfo(create_db_extension, [], {}),
        # *[TaskInfo(create_schema_tables_ddl, [project], {}) for project in project_list],
        # TaskInfo(get_deleted_worklog, [], {}),
        # TaskInfo(get_fields, [], {}),
        # TaskInfo(get_jira_users, [], {}),
        # TaskInfo(get_all_jira_boards, [], {}),
        # *[TaskInfo(proces2s_jira_versions, [project], {}) for project in project_list],
        # *[TaskInfo(get_sprint_details, [project], {}) for project in project_list],
        *[TaskInfo(process_jira_issues, [project, 'project', True], {}) for project in project_list],
        *[TaskInfo(upsert_issue_classification, [project], {}) for project in project_list],
        # *[TaskInfo(delete_worklog, [project], {}) for project in project_list],
        # TaskInfo(create_refresh_mv, [project_list], {})
        # *[TaskInfo(extract_workflows_standalone, [project], {}) for project in project_list],
    ]

    # Initialize managers
    display_manager = RichDisplayManager()
    progress_manager = ProgressManager()
    # Configure to show only 2 completed tasks instead of 4
    progress_manager.set_max_visible_completed(2)

    task_executor = TaskExecutor(q_container, my_logger, progress_manager)

    # Check how many tasks are completed
    completed_count = progress_manager.get_completed_tasks_count()
    my_logger.info(f"Completed tasks count: {completed_count}")

    try:
        if display_rich:
            # Initialize all UI components
            progress_manager.initialize_progress_bars(len(task_definitions))
            progress_manager.initialize_stats_table()
            if project_list:
                progress_manager.initialize_queue_table(project_list[0], q_container)

            # Create layout
            layout = display_manager.create_layout()

            # Add job tasks to progress tracker
            job_tasks = []
            for task_info in task_definitions:
                task_id = progress_manager.add_job_task(task_info)
                job_tasks.append(task_id)

            # Set up layout panels
            layout["overall"].update(Panel(
                Align.center(progress_manager.overall_progress, vertical="middle"),
                title="Overall Progress", border_style="green"
            ))

            if progress_manager.queue_status_table:
                layout["queues"].update(Panel(
                    progress_manager.queue_status_table,
                    title="Queue Status", border_style="cyan"
                ))

            layout["stats_progress"].update(Panel(
                Align.center(progress_manager.stats_progress, vertical="middle"),
                title="Stats Progress", border_style="yellow"
            ))

            layout["stats_table"].update(Panel(
                progress_manager.stats_table,
                title="Processing Statistics", border_style="magenta"
            ))

            layout["tasks"].update(Panel(
                Align.center(progress_manager.job_progress, vertical="middle"),
                title="[b]Task Progress", border_style="red"
            ))

            layout["monitor"].update(Panel(
                "No tasks exceeding the limit.",
                title="Task Monitor", border_style="red"
            ))

            # Start monitor task
            monitor_task = asyncio.create_task(
                monitor(
                    task_limit_sec=10,
                    exclude_names=("monitor", "monitor_circuit_breaker",),
                    monitor_panel=layout["monitor"]
                ),
                name="monitor"
            )

            mon_circuit_breaker = asyncio.create_task(
                monitor_circuit_breaker(my_logger),
                name="monitor_circuit_breaker"
            )

            # Execute tasks with Rich UI
            async with display_manager.managed_live(layout):
                await _execute_all_tasks(
                    task_definitions, job_tasks, task_executor,
                    progress_manager, my_logger
                )

            mon_circuit_breaker.cancel()
            monitor_task.cancel()

            try:
                await mon_circuit_breaker
            except asyncio.CancelledError:
                pass

            try:
                await monitor_task
            except asyncio.CancelledError:
                pass

        else:
            # Execute tasks without Rich UI
            await _execute_all_tasks(
                task_definitions, [], task_executor,
                progress_manager, my_logger
            )

    finally:
        # Ensure cleanup
        await task_executor.cleanup()
        my_logger.info("Process task completed")


async def _execute_all_tasks(
        task_definitions: List[TaskInfo],
        job_tasks: List[int],
        task_executor: TaskExecutor,
        progress_manager: ProgressManager,
        my_logger
):
    """Execute all tasks with progress tracking"""

    for i, task_info in enumerate(task_definitions):
        try:
            task_id = job_tasks[i] if i < len(job_tasks) else None
            result = await task_executor.execute_task(task_info, task_id)

            if progress_manager.overall_progress:
                progress_manager.overall_progress.update(
                    progress_manager.overall_task_id, completed=i + 1
                )

            # Clean up completed tasks more frequently to keep display manageable
            if (i + 1) % 2 == 0:  # Changed from 3 to 2 for more frequent cleanup
                progress_manager.cleanup_all_completed_tasks()
                my_logger.debug(f"Cleaned up completed tasks at iteration {i + 1}")

            # Additional cleanup every 5 tasks for thorough cleanup
            if (i + 1) % 5 == 0:
                progress_manager.cleanup_all_completed_tasks()
                my_logger.debug(f"Thorough cleanup at iteration {i + 1}")

        except Exception as e:
            my_logger.error(f"Task execution failed: {e}")
            # Continue with next task instead of failing completely
            continue
        finally:
            # Final cleanup at the end - use force cleanup for thorough cleanup
            # progress_manager.force_cleanup_all_tasks()
            # progress_manager.cleanup_all_completed_tasks()
            my_logger.info("Final cleanup of all completed tasks")


@inject
async def test_function(
        my_logger: Logger = Provide[LoggerContainer.logger],
        db_rw=Provide[DatabaseSessionManagerContainer.database_rw],
        app_container=Provide[ApplicationContainer],
        kp: PyKeePass = Provide[KeePassContainer.keepass_manager],
        extractor=Provide[IssueFieldsContainer.field_name_extractor],
        q_container: DynamicContainer = Provide[QueueContainer],
):
    # schemas = await app_container.database_rw().get_schemas_async()
    # print("Schemas:", schemas)
    #
    print(f"kp type = {type(kp)}")

    schemas = queue_container.schemas()
    print("Schemas:", schemas)
    q_container.config.override({"schema_name": "plat"})
    rprint(type(q_container.queue_selector()["queue_issues"]))

    x = app_container.database_rw().get_schemas()
    if 'information_schema' in x:
        x.remove('information_schema')

    rprint(f"schema list: {x}")
    rprint(f"schema list: {await app_container.database_rw().get_schemas_async()}")

    from faker import Faker
    # rprint(type(queues["queue_issues"]))
    # rprint(my_container.my_factory())
    # my_container.config.schema.override(["plat", "plp"])
    rprint(type(kp))
    rprint(app_container.config()["Database"])

    with app_container.database_rw().session() as pg_session:
        rprint(f"{dir(pg_session.bind.engine)}")
        rprint(f"Execution options = {pg_session.bind.engine.get_execution_options()}")
        rprint(f"Execution options = {pg_session.bind.engine.execution_options}")
        with pg_session.begin():
            stmt = select(func.count()).select_from(Issue)
            out = pg_session.execute(stmt).all()
            rprint(out)

    with db_rw.session() as pg_session:
        with pg_session.begin():
            rprint(pg_session.bind.url)
            pool = pg_session.bind.engine.pool

            idle_connections = pool._pool.qsize()
            # total_connections = len(pool._holders)
            # in_use = total_connections - idle_connections
            # Checking pool size
            rprint(f"Pool size: {pool.size()}")

            # Checking number of idle connections
            rprint(f"Idle connections: {pool.checkedin()}")

            # Checking the number of overflow connections
            rprint(f"Overflow connections: {pool.overflow()}")
            # Total: {total_connections}, In - use: {in_use}
            my_logger.info(
                f"Pool stats - , Idle: {idle_connections}"
            )

    # Initialize the Faker library
    fake = Faker()
    # Generate fake data
    data = {
        'Name': [fake.name() for _ in range(10)],
        'Address': [fake.address() for _ in range(10)],
        'Email': [fake.email() for _ in range(10)],
        'Phone Number': [fake.phone_number() for _ in range(10)],
        'Job': [fake.job() for _ in range(10)]
    }

    # Create a DataFrame
    df = pd.DataFrame(data)
    with DataframeDebugger() as debugger:
        debugger.debug_dataframe(df)

    # End of function


# Set up Passlib context with Argon2
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")


def generate_password_hash(password: str) -> str:
    return pwd_context.hash(password)


# Verify user input
def verify_password(input_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(input_password, hashed_password)


def cleanup_role(pg_session, role_name):
    try:
        # Revoke table privileges
        rprint(f"role name = {role_name}")
        revoke_tables = """
        DO $$
        DECLARE
            obj RECORD;
        BEGIN
            FOR obj IN
                SELECT table_schema, table_name
                FROM information_schema.role_table_grants
                WHERE grantee = :role_name
            LOOP
                EXECUTE format(
                'REVOKE ALL PRIVILEGES ON TABLE %I.%I FROM %I', obj.table_schema, obj.table_name, :role_name
                );
            END LOOP;
        END $$;
        """
        pg_session.execute(text(revoke_tables), {"role_name": role_name})

        # Revoke sequence privileges
        revoke_sequences = """
        DO $$
        DECLARE
            obj RECORD;
        BEGIN
            FOR obj IN
                SELECT schemaname AS sequence_schema, sequencename AS sequence_name
                FROM pg_sequences
                WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
            LOOP
                EXECUTE format(
                'REVOKE ALL PRIVILEGES ON SEQUENCE %I.%I FROM %I', obj.sequence_schema, obj.sequence_name, :role_name
                );
            END LOOP;
        END $$;
        """
        pg_session.execute(text(revoke_sequences), {"role_name": role_name})

        # Revoke permission for materialized views
        revoke_mv_privileges = f"""
        DO $$ 
        DECLARE 
            r RECORD;
        BEGIN 
            FOR r IN (SELECT schemaname, matviewname FROM pg_catalog.pg_matviews) 
            LOOP 
                EXECUTE format('REVOKE ALL PRIVILEGES ON %I.%I FROM %I', r.schemaname, r.matviewname, :role_name);
            END LOOP; 
        END $$;
        """
        pg_session.execute(text(revoke_mv_privileges), {"role_name": role_name})

        # Revoke schema-level privileges
        revoke_privileges = f"""
            REVOKE ALL PRIVILEGES ON SCHEMA public FROM {role_name}
        """
        pg_session.execute(text(revoke_privileges))

        # Drop the role
        pg_session.execute(text(f"DROP ROLE IF EXISTS {role_name}"))
        pg_session.commit()
        print(f"Role {role_name} cleaned up and dropped successfully.")

    except Exception as e:
        print(f"Error cleaning up role {role_name}: {e}")
        pg_session.rollback()


@inject
def cleanup_entries(
        schema_name: str,
        kp: PyKeePass = Provide[KeePassContainer.keepass_manager],
        app_container: DynamicContainer = Provide[ApplicationContainer]

):
    password = Prompt.ask("Enter your password", password=True)

    if verify_password(
            password,
            "$argon2id$v=19$m=65536,t=3,p=4$NCYkBOAcI6S01npPSan1fg$dFRfGkx5J9HbzljOzijM53ywRfqQb7MA+leBXXnH8YM"
    ):
        proceed = Confirm.ask(f"Do you want to clean-up {schema_name} entries")
        if proceed:
            entry = kp.find_entries(title=f"{schema_name}_rw", first=True)
            if entry:
                kp.delete_entry(entry)
                rprint(f"Deleted {schema_name}_rw")
                kp.save()

            entry = kp.find_entries(title=f"{schema_name}_ro", first=True)
            if entry:
                kp.delete_entry(entry)
                rprint(f"Deleted {schema_name}_ro")
                kp.save()

            try:
                app_container.schema.override('public')
                with app_container.database_rw().update_schema('public').session() as pg_session:
                    # Drop schema
                    pg_session.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))
                    pg_session.commit()
                    rprint(f"Schema {schema_name} dropped successfully.")

                    # Drop roles
                    cleanup_role(pg_session, f"{schema_name}_rw")
                    rprint(f"Role {schema_name}_rw dropped successfully.")
                    cleanup_role(pg_session, f"{schema_name}_ro")
                    rprint(f"Role {schema_name}_ro dropped successfully.")
                    pg_session.commit()
            except Exception as e:
                handle_exception(e)
        else:
            rprint(f"User opted No")

    else:
        rprint("[red]Authentication Failed[/red]")

    # if kp.find_entries(title=f"{schema_name}_rw", first=True):
    #


@inject
def check_upsert(
        app_container: DynamicContainer = Provide[ApplicationContainer]
):
    now = datetime.now().isoformat()  # Convert to ISO format
    date_literal = literal(now)
    rprint(f"date_literal = {date_literal}")

    # Compile to SQL
    compiled_literal = date_literal.compile(dialect=dialect(), compile_kwargs={"literal_binds": True})
    rprint(compiled_literal)

    data = [
        [136529, "PLAT-35697", "Test", "2022-03-23", "2022-03-21"]
    ]
    df = pd.DataFrame(data, columns=["initiative_id", "initiative_key", "project", "created", "updated"])
    df["created"] = pd.to_datetime(df["created"])
    df["updated"] = pd.to_datetime(df["updated"])

    conflict_condition = and_(
        InitiativeAttribute.updated < insert(InitiativeAttribute).excluded.updated
    )

    with (app_container.database_rw().update_schema('plat').session() as pg_session):
        upsert(
            pg_session, InitiativeAttribute, df, no_update_cols=("release", "feature", "created"),
            # conflict_condition=["updated"],
            conflict_condition=conflict_condition,
        )
        stmt = select(
            InitiativeAttribute.project, InitiativeAttribute.updated
        ).filter(InitiativeAttribute.initiative_id == 136529)
        result = pg_session.execute(stmt)
        rprint(result.fetchall())
        pg_session.rollback()

        stmt = insert(InitiativeAttribute).values(df.to_dict(orient="records"))
        stmt = stmt.on_conflict_do_update(
            index_elements=["initiative_id"],
            where=conflict_condition,
            set_={
                "initiative_key": stmt.excluded.initiative_key,
                "project": stmt.excluded.project,
                "updated": stmt.excluded.updated,
            },
        )
        pg_session.execute(stmt)
        rprint(f"compiled query")
        rprint(compile_query(stmt))

        stmt = select(InitiativeAttribute.project, InitiativeAttribute.updated).filter(
            InitiativeAttribute.initiative_id == 136529)
        result = pg_session.execute(stmt)
        rprint(result.fetchall())
        pg_session.rollback()


@inject
async def split_jql_by_count(
        jql: str,
        max_batch_size: int = 1000,
        num_batches: int = 10,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> tuple[list[Any], int] | tuple[list[str], Any]:
    """
        Splits a JQL query into multiple batches based on date ranges to handle large result sets.

        This function will adaptively split the JQL query into smaller batches that each return
        a more manageable number of results. It uses approximate count API calls to distribute
        records evenly across batches and ensures each batch contains records.

        Args:
            jql: The original JQL query string that needs to be split
            max_batch_size: Maximum number of records allowed in a single batch
            num_batches: Target number of batches to split the query into
            jira_entry: JIRA connection details
            my_logger: Logger instance for tracking progress and errors

        Returns:
            A list of JQL strings representing the batched queries, or an empty list if splitting
            is not needed or not possible
        """
    try:
        my_logger.info(f"Starting JQL split operation with target of {num_batches} batches")
        my_logger.debug(f"Original JQL: {jql}")
        # Check approximate count
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(headers=jira_entry.custom_properties, timeout=timeout) as http_session:
            response = await fetch_with_retries_post(
                http_session,
                f"{jira_entry.url}/rest/api/3/search/approximate-count",
                json_payload={"jql": jql}
            )

            if not response or not response.get('success'):
                my_logger.error(f"Failed to get approximate count: {response}")
                return [], -1

            total_count = response['result']['count']
            my_logger.info(f"Approximate count for JQL: {total_count}")

            # If count is less than max_batch_size, return original JQL
            if total_count <= max_batch_size:
                return [jql], total_count

            # Extract date range from JQL if it exists
            # Match both created and updated fields
            date_pattern = r'(created|updated)\s*>\s*[\'"]([^\'"]*)[\'"]\s*(?:and\s*(created|updated)\s*<\s*[\'"]([^\'"]*)[\'"]\s*)?'
            match = re.search(date_pattern, jql)

            if not match:
                my_logger.warning("Could not find date range in JQL, cannot split")
                return [], -1

            field_start, start_date_str, field_end, end_date_str = match.groups()

            # If end date is not specified, use current date
            if not end_date_str:
                end_date = datetime.now()
                field_end = field_start  # Use same field for end date
            else:
                try:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d %H:%M')
                except ValueError:
                    try:
                        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                    except ValueError:
                        my_logger.error(f"Could not parse end date: {end_date_str}")
                        return [], -1

            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d %H:%M')
            except ValueError:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                except ValueError:
                    my_logger.error(f"Could not parse start date: {start_date_str}")
                    return [], -1

            # Use binary search to find date intervals that more evenly distribute records
            return await adaptive_split_jql(jql, start_date, end_date, field_start, field_end,
                                            num_batches, http_session, jira_entry, my_logger), total_count

    except Exception as e:
        # my_logger.error(f"Error splitting JQL: {e}", exc_info=True)
        handle_exception(e)
        return [], -1


async def adaptive_split_jql(
        jql: str,
        start_date: datetime,
        end_date: datetime,
        field_start: str,
        field_end: str,
        num_batches: int,
        http_session,
        jira_entry: EntryDetails,
        my_logger: Logger
) -> list[str]:
    """
    Adaptively split JQL by finding date boundaries that better distribute records.

    This function uses a binary search approach to find date boundaries that evenly
    distribute records across batches. It validates each batch to ensure it contains
    records and excludes any empty batches.

    Args:
        jql: Original JQL query string
        start_date: Start date boundary for the query
        end_date: End date boundary for the query
        field_start: Field name for the start date criteria (created/updated)
        field_end: Field name for the end date criteria (created/updated)
        num_batches: Target number of batches to create
        http_session: Active HTTP client session
        jira_entry: JIRA connection details
        my_logger: Logger instance for tracking progress

    Returns:
        List of JQL strings representing valid batches (with records)
    """
    total_seconds = (end_date - start_date).total_seconds()

    # First get total count for the full date range
    full_range_jql = create_date_range_jql(jql, start_date, end_date, field_start, field_end)
    full_count_response = await fetch_with_retries_post(
        http_session,
        f"{jira_entry.url}/rest/api/3/search/approximate-count",
        json_payload={"jql": full_range_jql}
    )

    if not full_count_response or not full_count_response.get('success'):
        my_logger.error(f"Failed to get full range count: {full_count_response}")
        return []

    total_count = full_count_response['result']['count']
    if total_count == 0:
        return []

    # If total count is still manageable, just return original JQL
    if total_count <= num_batches:
        return [full_range_jql]

    # Find split points that distribute records more evenly
    target_per_batch = total_count / num_batches
    my_logger.info(f"Targeting approximately {target_per_batch} records per batch")

    # Find batch boundaries
    batch_boundaries = [start_date]
    current_date = start_date

    for i in range(1, num_batches):
        # Binary search to find a date that gives approximately i*target_per_batch records
        target_records = int(i * target_per_batch)
        low_date = current_date
        high_date = end_date

        # Maximum 10 iterations of binary search to prevent excessive API calls
        mid_date = low_date + (high_date - low_date) / 2
        for _ in range(10):
            mid_date = low_date + (high_date - low_date) / 2

            # Create JQL for records from start_date to mid_date
            test_jql = create_date_range_jql(jql, start_date, mid_date, field_start, field_end)

            # Get count for this date range
            count_response = await fetch_with_retries_post(
                http_session,
                f"{jira_entry.url}/rest/api/3/search/approximate-count",
                json_payload={"jql": test_jql}
            )

            if not count_response or not count_response.get('success'):
                my_logger.warning(f"Failed to get count for date range, using estimate: {count_response}")
                # Fall back to linear estimation
                ratio = i / num_batches
                mid_date = start_date + timedelta(seconds=ratio * total_seconds)
                break

            mid_count = count_response['result']['count']

            # Check if we're close enough to the target
            if abs(mid_count - target_records) < (target_per_batch * 0.1) or (
                    high_date - low_date).total_seconds() < 60:
                break

            if mid_count < target_records:
                low_date = mid_date
            else:
                high_date = mid_date

        batch_boundaries.append(mid_date)
        current_date = mid_date

    batch_boundaries.append(end_date)

    # Create JQL for each batch and validate it has records
    valid_batched_jqls = []

    for i in range(len(batch_boundaries) - 1):
        batch_start = batch_boundaries[i]
        batch_end = batch_boundaries[i + 1]

        batch_jql = create_date_range_jql(jql, batch_start, batch_end, field_start, field_end)

        # Verify this batch has records
        count_response = await fetch_with_retries_post(
            http_session,
            f"{jira_entry.url}/rest/api/3/search/approximate-count",
            json_payload={"jql": batch_jql}
        )

        if count_response and count_response.get('success'):
            batch_count = count_response['result']['count']
            if batch_count > 0:
                valid_batched_jqls.append(batch_jql)
                my_logger.info(f"Batch {i + 1}: {batch_count} records")
            else:
                my_logger.warning(f"Skipping batch {i + 1} with 0 records")

    return valid_batched_jqls


def create_date_range_jql(jql: str, start: datetime, end: datetime, field_start: str, field_end: str) -> str:
    """Create a JQL with the specified date range."""
    # Extract the original date range pattern from the JQL
    date_pattern = r'(created|updated)\s*>\s*[\'"]([^\'"]*)[\'"]\s*(?:and\s*(created|updated)\s*<\s*[\'"]([^\'"]*)[\'"]\s*)?'
    match = re.search(date_pattern, jql)

    if not match:
        return jql

    original_date_part = match.group(0)

    # Create the new date range
    new_date_range = f"{field_start} > '{start.strftime('%Y-%m-%d %H:%M')}' and {field_end} < '{end.strftime('%Y-%m-%d %H:%M')}'"

    # Replace the original date range with the new one
    return jql.replace(original_date_part, new_date_range)
    
logger_container = LoggerContainer()

# logger_container.wire(
#     modules=["data_pipeline.utility_code", "data_pipeline.utilities.profilers"],
# )

logger_container.init_resources()

extract_container = IssueFieldsContainer()
# extract_container.init_resources()

# Register the stop method
# LoggerContainer.register_listener_stop()

keepass_container = KeePassContainer()
keepass_container.wire([__name__])
keepass_container.init_resources()
# Initialize DatabaseSessionManagerContainer with dependencies from KeePassContainer
database_container = DatabaseSessionManagerContainer()
database_container.wire([__name__])
database_container.init_resources()
database_container.pg_rw_entry.override(keepass_container.pg_rw)
database_container.pg_ro_entry.override(keepass_container.pg_ro)
database_container.schema.override('public')

jira_container = JiraEntryDetailsContainer()
jira_container.wire([__name__])
jira_container.init_resources()
jira_container.keepass.override(keepass_container.keepass_manager)

application_container = ApplicationContainer()
application_container.wire([__name__])
application_container.init_resources()

queue_container = QueueContainer()
queue_container.wire([__name__])
queue_container.database_rw.override(application_container.database_rw())

# Global instance
global_circuit_breaker = GlobalCircuitBreaker()


# rprint(container.my_factory())
# Attach listeners to the engine's pool
# event.listen(Pool, "checkout", before_checkout)
# event.listen(Pool, "invalidate", on_connection_invalidated)
# event.listen(Pool, "checkin", after_checkout, named=True)

# @listens_for(Pool, "connect")
# def my_on_connect(dbapi_con, connection_record):
#     print("New DBAPI connection:", dbapi_con)
#     print("Connection record", connection_record)
logging.captureWarnings(True)

if __name__ == '__main__':
    os.environ['DEBUG'] = "no-debug"
    desired_width = 320
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', desired_width)
    np.set_printoptions(linewidth=desired_width)

    pd.set_option('display.max_columns', 10)
    # asyncio.get_event_loop().set_debug(True)
    # logging.getLogger('asyncio').setLevel(logging.DEBUG)
    #
    # logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
    # logging.getLogger('sqlalchemy.pool').setLevel(logging.DEBUG)
    # logging.getLogger('sqlalchemy.orm').setLevel(logging.INFO)

    # Only set the event loop policy if running on Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    # asyncio.run(get_all_jira_boards())
    # add_to_db_all_jira_boards()
    # asyncio.run(test_function())
    #
    # for schema in ["train"]:
    #     cleanup_entries(schema)
    # deadlock_task = asyncio.create_task(monitor_deadlocks(), name="monitor_deadlocks")
    # result, total_count = asyncio.run(
    #     split_jql_by_count(
    #         jql="project = PLAT and created > '2019-01-01 00:00' and created < '2025-05-31 23:59'")
    #         )
    # print(result, total_count)
    # exit(0)

    asyncio.run(process_task(True, projects=("plat","cpp")), debug=False)
    # check_upsert()
    # asyncio.run(get_deleted_worklog())
